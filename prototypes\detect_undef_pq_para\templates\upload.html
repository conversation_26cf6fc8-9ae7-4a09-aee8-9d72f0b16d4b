{% extends "base.html" %}

{% block title %}File Upload - {{ selected_app.name }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10 col-lg-8">
        <!-- User Info Card -->
        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-user me-2"></i>
                    Welcome, {{ user_id }}
                </h5>
                <p class="card-text text-muted">
                    <i class="{{ selected_app.icon }} me-1"></i>
                    Application: <strong>{{ selected_app.name }}</strong><br>
                    <small>{{ selected_app.description }}</small><br>
                    Session ID: <code>{{ session_id }}</code>
                </p>
                <div class="mt-2">
                    <a href="/" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Change Application
                    </a>
                </div>
            </div>
        </div>

        <!-- File Upload Card -->
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="card-title mb-0">
                    <i class="fas fa-upload me-2"></i>
                    {% if selected_app_id == "pq_config_validator" %}
                        Upload PQ Configuration File for Analysis
                    {% elif selected_app_id == "grid_comparison_plotter" %}
                        Upload TC Flash Log for IDK-IC In-and-Out
                    {% elif selected_app_id == "log_parser_plotter" %}
                        Upload TC Flash Log for Time-Series Visualization
                    {% else %}
                        Upload File for {{ selected_app.name }}
                    {% endif %}
                </h4>
            </div>
            <div class="card-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="file-upload-area mb-3">
                        <div class="text-center">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            {% if selected_app_id == "pq_config_validator" %}
                                <h5>Drop your PQ configuration file here or click to browse</h5>
                                <p class="text-muted">
                                    Supported formats: .cfg, .txt
                                    <br>
                                    Maximum file size: 10MB
                                </p>
                            {% elif selected_app_id == "grid_comparison_plotter" %}
                                <h5>Drop your TC Flash log file here or click to browse</h5>
                                <p class="text-muted">
                                    Supported formats: .txt, .log (must contain Config and DM values sections)
                                    <br>
                                    Maximum file size: 10MB
                                </p>
                            {% elif selected_app_id == "firmware_log_visualizer" %}
                                <h5>Drop your firmware log file here or click to browse</h5>
                                <p class="text-muted">
                                    Supported formats: .txt, .log (Dolby debug logs with numerical parameters)
                                    <br>
                                    Maximum file size: 10MB
                                </p>
                            {% else %}
                                <h5>Drop your file here or click to browse</h5>
                                <p class="text-muted">
                                    Supported formats: .txt, .cfg, .log
                                    <br>
                                    Maximum file size: 10MB
                                </p>
                            {% endif %}
                        </div>
                        <input
                            type="file"
                            id="fileInput"
                            name="file"
                            {% if selected_app_id == "pq_config_validator" %}
                                accept=".txt,.cfg"
                            {% elif selected_app_id in ["grid_comparison_plotter", "firmware_log_visualizer"] %}
                                accept=".txt,.log"
                            {% else %}
                                accept=".txt,.cfg,.log"
                            {% endif %}
                            style="display: none;"
                            required
                        >
                    </div>
                    
                    <div class="file-info" style="display: none;"></div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-search me-2"></i>
                            Analyze File for Undefined Parameters
                        </button>
                    </div>
                </form>
                
                <div id="alertContainer" class="mt-3"></div>
            </div>
        </div>

        <!-- Processing Spinner -->
        <div class="processing-spinner text-center mt-4">
            <div class="card">
                <div class="card-body">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5>Processing your file...</h5>
                    <p class="text-muted">
                        Please wait while we analyze your file for undefined parameters.
                        This may take a few moments.
                    </p>
                </div>
            </div>
        </div>

        <!-- Results Container -->
        <div class="results-container mt-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Analysis Results
                    </h5>
                </div>
                <div class="card-body" id="resultsContent">
                    <!-- Results will be populated here -->
                </div>
                <div class="card-footer">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download me-2"></i>
                            Download Report
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="downloadResults('html')">
                                <i class="fas fa-file-code me-2"></i>HTML Report
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="downloadResults('markdown')">
                                <i class="fas fa-file-alt me-2"></i>Markdown Report
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if selected_app_id == "pq_config_validator" %}
                        How the Analysis Works
                    {% elif selected_app_id == "grid_comparison_plotter" %}
                        How IDK-IC In-and-Out Works
                    {% elif selected_app_id == "log_parser_plotter" %}
                        How Time-Series Visualization Works
                    {% else %}
                        How the Processing Works
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                {% if selected_app_id == "pq_config_validator" %}
                    <ol class="mb-0">
                        <li><strong>File Upload:</strong> Upload your text/configuration file</li>
                        <li><strong>Parameter Detection:</strong> Our tool scans the file for parameter references</li>
                        <li><strong>Validation:</strong> Each parameter is checked against known definitions</li>
                        <li><strong>Results:</strong> Undefined parameters are listed with their line numbers</li>
                    </ol>
                    <div class="alert alert-info mt-3 mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Tip:</strong> Make sure your file contains parameter definitions or references
                        in a recognizable format for best results.
                    </div>
                {% elif selected_app_id == "grid_comparison_plotter" %}
                    <ol class="mb-0">
                        <li><strong>File Upload:</strong> Upload your TC Flash log file</li>
                        <li><strong>Log Parsing:</strong> Extract "Config values:" and "DM values:" sections</li>
                        <li><strong>Grid Generation:</strong> Create comprehensive comparison plots with grid layout</li>
                        <li><strong>Visualization:</strong> Display interactive plots showing parameter relationships</li>
                    </ol>
                    <div class="alert alert-info mt-3 mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Tip:</strong> Ensure your log file contains "Config values:" and "DM values:" sections
                        for optimal visualization results.
                    </div>
                {% elif selected_app_id == "log_parser_plotter" %}
                    <ol class="mb-0">
                        <li><strong>File Upload:</strong> Upload your TC Flash log file</li>
                        <li><strong>Log Parsing:</strong> Extract time-series parameter data</li>
                        <li><strong>Time-Series Analysis:</strong> Process parameter changes over time</li>
                        <li><strong>Visualization:</strong> Generate time-series plots showing parameter evolution</li>
                    </ol>
                    <div class="alert alert-info mt-3 mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Tip:</strong> Log files with timestamp information will produce more detailed
                        time-series visualizations.
                    </div>
                {% else %}
                    <ol class="mb-0">
                        <li><strong>File Upload:</strong> Upload your file</li>
                        <li><strong>Processing:</strong> File is processed using {{ selected_app.name }}</li>
                        <li><strong>Analysis:</strong> Content is analyzed based on the selected tool</li>
                        <li><strong>Results:</strong> Output is generated according to the tool's capabilities</li>
                    </ol>
                    <div class="alert alert-info mt-3 mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Tip:</strong> Check the application documentation for specific file format requirements.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
const sessionId = "{{ session_id }}";
window.sessionId = sessionId;

document.getElementById('uploadForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const fileInput = document.getElementById('fileInput');
    const file = fileInput.files[0];
    
    if (!file) {
        showAlert('Please select a file to upload', 'danger');
        return;
    }
    
    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
        showAlert('File size must be less than 10MB', 'danger');
        return;
    }
    
    // Show loading state
    showLoading(true);
    showResults(false);
    
    try {
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await fetch(`/api/upload/${sessionId}`, {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (response.ok) {
            displayResults(result);
            showResults(true);
        } else {
            showAlert(result.detail || 'Upload failed', 'danger');
        }
    } catch (error) {
        showAlert('Network error. Please try again.', 'danger');
    } finally {
        showLoading(false);
    }
});

function displayResults(result) {
    const resultsContent = document.getElementById('resultsContent');
    resultsContent.innerHTML = formatResults(result);
}

function resetForm() {
    document.getElementById('uploadForm').reset();
    clearFile();
    showResults(false);
    document.getElementById('alertContainer').innerHTML = '';
}


</script>
{% endblock %}

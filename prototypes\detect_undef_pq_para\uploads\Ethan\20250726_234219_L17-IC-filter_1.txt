
130|console:/ #
130|console:/ # su
ys/bus/rpmsg/devices/virtio*.pqu-vuart.-*/mtk_dbg/record_log                  <
console:/ # dmesg -w | grep DolbyDBG
[ 3087.283354] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.283356] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.283358] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.283360] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.283362] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.283364] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.283366] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.283368] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.283370] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3087.283373] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3087.283375] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3087.283377] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3087.283378] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3087.283380] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3087.283382] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3087.283384] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3087.283385] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3087.283387] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3087.283389] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3087.283390] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3087.298938] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.299002] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.299005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.299008] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.299010] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.299013] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.299015] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.299017] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.299020] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.299022] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.299024] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.299026] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.299028] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.299030] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.299032] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.299034] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.299036] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.299038] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.299040] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.299042] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.299044] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.299046] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.299048] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.299050] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.299052] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.299054] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.299056] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.299058] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.299060] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.299064] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.299067] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3087.299069] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3087.299071] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3087.299073] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3087.299075] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3087.299076] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3087.299079] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3087.299081] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3087.299083] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3087.299085] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3087.299087] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3087.299089] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3087.299091] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3087.299092] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3087.299094] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.299096] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.299098] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.299100] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.299102] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.299104] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.299106] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.299108] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.299110] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.299112] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.299114] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3087.299118] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3087.299119] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3087.299121] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3087.299123] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3087.299124] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3087.299126] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3087.299128] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3087.299130] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3087.299131] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3087.299133] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3087.299135] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3087.315593] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.315641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.315644] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.315646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.315648] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.315651] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.315653] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.315655] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.315657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.315659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.315661] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.315663] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.315665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.315667] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.315669] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.315671] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.315673] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.315675] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.315677] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.315679] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.315681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.315683] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.315685] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.315687] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.315689] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.315691] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.315693] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.315695] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.315697] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.315699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.315701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3087.315703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3087.315705] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3087.315707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3087.315709] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3087.315710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3087.315715] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3087.315717] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3087.315719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3087.315721] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3087.315723] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3087.315724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3087.315726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3087.315728] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3087.315730] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.315732] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.315734] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.315736] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.315738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.315740] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.315742] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.315745] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.315747] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.315749] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.315751] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3087.315754] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3087.315756] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3087.315757] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3087.315759] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3087.315761] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3087.315763] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3087.315764] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3087.315766] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3087.315768] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3087.315769] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3087.315771] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3087.332248] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.332292] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.332315] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.332318] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.332321] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.332323] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.332325] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.332328] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.332330] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.332332] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.332334] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.332336] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.332338] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.332340] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.332342] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.332344] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.332346] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.332348] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.332350] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.332352] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.332354] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.332356] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.332359] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.332361] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.332363] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.332365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.332367] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.332369] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.332371] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.332376] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.332378] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3087.332380] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3087.332382] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3087.332384] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3087.332386] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3087.332388] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3087.332390] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3087.332391] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3087.332393] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3087.332395] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3087.332397] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3087.332399] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3087.332400] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3087.332402] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3087.332404] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.332406] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.332408] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.332410] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.332412] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.332414] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.332416] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.332418] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.332420] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.332422] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.332424] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3087.332427] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3087.332429] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3087.332431] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3087.332432] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3087.332434] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3087.332436] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3087.332438] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3087.332439] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3087.332441] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3087.332443] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3087.332445] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3087.348958] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.349000] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.349017] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.349020] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.349043] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.349045] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.349047] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.349050] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.349052] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.349055] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.349057] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.349059] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.349061] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.349063] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.349065] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.349067] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.349069] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.349071] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.349073] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.349075] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.349077] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.349079] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.349081] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.349086] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.349088] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.349090] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.349092] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.349094] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.349096] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.349099] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.349101] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3087.349103] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3087.349106] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3087.349108] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3087.349110] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3087.349112] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3087.349114] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3087.349115] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3087.349117] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3087.349119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3087.349121] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3087.349123] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3087.349124] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3087.349126] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3087.349128] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.349130] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.349132] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.349134] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.349136] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.349138] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.349140] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.349142] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.349144] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.349146] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.349148] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3087.349151] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3087.349153] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3087.349155] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3087.349156] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3087.349158] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3087.349160] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3087.349161] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3087.349163] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3087.349165] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3087.349167] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3087.349168] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3087.365589] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.365647] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.365664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.365687] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.365689] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.365692] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.365694] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.365696] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.365698] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.365700] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.365702] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.365704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.365707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.365709] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.365711] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.365713] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.365715] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.365716] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.365718] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.365720] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.365723] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.365724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.365726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.365731] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.365734] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.365736] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.365738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.365740] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.365742] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.365744] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.365747] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3087.365749] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3087.365751] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3087.365753] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3087.365755] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3087.365756] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3087.365758] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3087.365760] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3087.365762] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3087.365764] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3087.365766] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3087.365767] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3087.365769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3087.365771] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3087.365773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.365775] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.365777] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.365779] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.365781] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.365783] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.365785] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.365787] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.365789] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.365791] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.365793] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3087.365796] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3087.365798] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3087.365799] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3087.365801] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3087.365803] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3087.365805] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3087.365806] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3087.365808] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3087.365810] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3087.365812] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3087.365813] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3087.382340] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.382390] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.382394] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.382396] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.382399] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.382401] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.382403] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.382405] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.382408] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.382410] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.382412] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.382414] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.382416] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.382418] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.382420] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.382422] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.382424] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.382426] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.382428] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.382430] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.382433] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.382435] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.382437] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.382438] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.382440] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.382442] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.382444] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.382446] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.382448] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.382450] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.382452] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3087.382454] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3087.382456] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3087.382458] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3087.382460] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3087.382462] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3087.382466] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3087.382468] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3087.382470] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3087.382472] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3087.382473] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3087.382475] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3087.382477] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3087.382479] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3087.382481] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.382483] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.382485] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.382487] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.382489] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.382491] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.382493] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.382495] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.382497] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.382499] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.382501] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3087.382504] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3087.382506] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3087.382508] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3087.382509] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3087.382511] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3087.382513] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3087.382515] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3087.382516] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3087.382518] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3087.382520] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3087.382521] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3087.399358] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3087.399361] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3087.399363] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3087.399365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3087.399368] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3087.399370] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3087.399372] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3087.399376] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.399379] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.399381] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.399383] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.399385] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.399387] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.399389] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.399391] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.399393] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.399395] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.399397] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3087.399401] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3087.399402] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3087.399404] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3087.399406] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3087.399408] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3087.399409] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3087.399411] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3087.399413] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3087.399414] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3087.399416] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3087.399418] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3087.415972] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.416009] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.416026] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.416095] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.416097] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.416100] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.416102] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.416104] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.416106] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.416109] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.416111] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.416113] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.416115] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.416117] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.416119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.416121] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.416123] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.416125] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.416127] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.416129] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.416131] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.416133] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.416135] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.416139] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.416142] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.416144] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.416146] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.416148] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.416150] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.416152] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.416153] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3087.416155] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3087.416157] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3087.416159] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3087.416161] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3087.416163] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3087.416165] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3087.416166] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3087.416168] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3087.416170] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3087.416172] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3087.416173] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3087.416175] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3087.416177] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3087.416179] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.416181] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.416183] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.416185] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.416187] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.416188] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.416190] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.416192] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.416194] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.416196] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.416198] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3087.416201] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3087.416203] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3087.416205] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3087.416206] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3087.416208] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3087.416210] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3087.416211] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3087.416213] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3087.416215] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3087.416217] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3087.416218] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3087.432396] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.432437] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.432454] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.432457] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.432459] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.432481] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.432484] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.432486] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.432489] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.432491] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.432493] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.432495] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.432498] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.432500] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.432502] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.432504] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.432506] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.432508] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.432510] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.432513] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.432515] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.432517] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.432519] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.432523] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.432525] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.432527] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.432530] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.432532] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.432534] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.432536] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.432538] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3087.432540] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3087.432542] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3087.432543] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3087.432545] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3087.432547] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3087.432549] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3087.432551] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3087.432553] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3087.432555] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3087.432557] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3087.432558] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3087.432560] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3087.432562] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3087.432564] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.432566] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.432568] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.432570] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.432572] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.432574] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.432576] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.432578] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.432580] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.432582] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.432584] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3087.432588] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3087.432589] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3087.432591] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3087.432593] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3087.432594] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3087.432596] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3087.432598] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3087.432600] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3087.432601] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3087.432603] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3087.432605] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3087.449694] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.449711] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.449714] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.449717] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.449719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.449721] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.449723] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.449726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.449728] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.449730] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.449732] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.449735] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.449737] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.449739] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.449741] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.449743] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.449745] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.449747] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.449749] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.449751] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.449753] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.449755] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.449757] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.449759] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.449761] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.449763] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.449765] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.449767] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.449769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.449772] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.449774] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3087.449776] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3087.449778] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3087.449780] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3087.449781] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3087.449783] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3087.449785] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3087.449787] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3087.449789] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3087.449791] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3087.449792] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3087.449794] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3087.449796] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3087.449798] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3087.449800] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.449802] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.449804] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.449806] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.449808] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.449810] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.449812] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.449816] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.449819] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.449821] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.449823] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3087.449826] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3087.449828] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3087.449830] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3087.449831] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3087.449833] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3087.449835] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3087.449836] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3087.449838] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3087.449840] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3087.449842] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3087.449843] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3087.465833] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.465885] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.465888] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.465891] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.465893] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.465895] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.465897] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.465899] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.465902] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.465904] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.465906] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.465908] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.465910] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.465912] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.465914] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.465916] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.465918] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.615727] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3087.615729] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3087.650197] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.650253] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.650275] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.650278] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.650280] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.650282] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.650285] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.650287] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.650289] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.650291] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.650294] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.650296] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.650298] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.650300] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.650302] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.650304] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.650306] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.650308] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.650310] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.650312] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.650314] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.650316] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.650318] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.650322] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.650325] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.650327] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.650329] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.899613] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.899615] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.899617] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.899619] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.899621] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.899623] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3087.899625] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3087.899627] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3087.899629] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3087.899631] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3087.899633] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3087.899635] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3087.899637] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3087.899639] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3087.899641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3087.899642] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3087.899644] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3087.899646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3087.899648] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3087.899650] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3087.899652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3087.899654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3087.899656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3087.899658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3087.899660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3087.899662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3087.899664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3087.899666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3087.899668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3087.899670] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3087.899673] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3087.899675] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3087.899677] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3087.899678] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3087.899680] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3087.899682] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3087.899684] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3087.899685] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3087.899687] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3087.899689] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3087.899691] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3088.116517] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3088.116519] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3088.116521] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3088.116523] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3088.116526] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3088.116528] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3088.116530] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3088.116531] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3088.116533] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3088.116535] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3088.116537] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3088.116539] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3088.116541] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3088.116542] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3088.116544] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3088.116546] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3088.116548] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3088.116550] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3088.116552] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3088.116554] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3088.116556] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3088.116557] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3088.116559] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3088.116561] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3088.116563] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3088.116565] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3088.116567] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3088.116569] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3088.116573] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3088.116574] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3088.116576] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3088.116578] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3088.116579] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3088.116581] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3088.116583] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3088.116585] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3088.116586] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3088.116588] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3088.116590] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3088.406903] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3088.406905] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3088.406907] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3088.406909] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3088.406910] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3088.406912] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3088.406914] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3088.406916] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3088.406918] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3088.406920] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3088.406921] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3088.406923] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3088.406925] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3088.406927] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3088.406929] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3088.406931] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3088.406933] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3088.406935] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3088.406936] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3088.406938] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3088.406940] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3088.406942] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3088.406944] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3088.406946] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3088.406948] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3088.406952] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3088.406953] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3088.406955] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3088.406957] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3088.406958] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3088.406960] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3088.406962] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3088.406964] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3088.406965] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3088.406967] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3088.406969] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3088.960775] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3088.960777] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3088.960780] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3088.960782] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3088.960784] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3088.960786] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3088.960789] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3088.960791] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3088.960793] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3088.960795] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3088.960797] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3088.960800] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3088.960801] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3088.960804] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3088.960806] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3088.960808] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3088.960810] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3088.961228] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3088.961246] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3088.961248] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3088.961290] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3088.961292] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3088.961294] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3088.961296] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3088.961300] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3088.961303] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3088.961305] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3088.961307] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3088.961309] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3088.961312] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3088.961314] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3088.961315] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3088.961317] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3088.961319] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3088.961321] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3088.961323] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3088.961325] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3088.961328] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3089.284638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3089.284643] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3089.284645] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3089.284647] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3089.284650] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3089.284652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3089.284654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3089.284656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3089.284658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3089.284660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3089.284662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3089.284664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3089.284666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3089.284668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3089.284670] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3089.284672] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3089.284673] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3089.284675] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3089.284677] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3089.284679] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3089.284681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3089.284683] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3089.284685] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3089.284686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3089.284688] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3089.284690] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3089.284692] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3089.284694] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3089.284695] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3089.284697] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3089.284699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3089.284701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3089.284703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3089.284705] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3089.284707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3089.284709] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3089.284711] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3089.284713] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3089.618080] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3089.634698] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3089.634702] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3089.634704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3089.634707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3089.634709] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3089.634711] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3089.634713] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3089.634716] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3089.634718] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3089.634720] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3089.634722] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3089.634724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3089.634726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3089.634728] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3089.634730] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3089.634732] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3089.634734] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3089.634736] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3089.634738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3089.634740] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3089.634742] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3089.634743] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3089.634745] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3089.634747] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3089.634749] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3089.634751] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3089.634752] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3089.634754] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3089.634756] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3089.634758] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3089.634760] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3089.634762] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3089.634772] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3089.634775] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3089.634777] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3089.984961] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3089.984963] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3089.984965] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3089.984968] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3089.984970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3089.984972] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3089.984974] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3089.984976] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3089.984979] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3089.984980] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3089.984982] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3089.984984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3089.984986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3089.984988] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3089.984990] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3089.984992] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3089.984994] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3089.984996] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3089.985001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3089.985004] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3089.985006] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3089.985008] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3089.985010] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3089.985012] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3089.985014] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3089.985016] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3089.985018] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3089.985020] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3089.985022] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3089.985024] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3089.985026] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3089.985028] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3089.985029] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3089.985031] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3089.985033] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3089.985035] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3089.985037] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3090.301154] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3090.301157] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3090.301173] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3090.301175] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3090.301177] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3090.301194] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3090.301196] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3090.301213] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3090.301215] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3090.301218] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3090.301237] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3090.301260] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3090.301262] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3090.301265] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3090.301267] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3090.301269] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3090.301272] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3090.301274] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3090.301276] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3090.301278] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3090.301280] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3090.301282] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3090.301284] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3090.301285] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3090.301287] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3090.301289] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3090.301291] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3090.301293] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3090.301294] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3090.301296] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3090.301298] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3090.301300] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3090.301302] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3090.301304] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3090.301306] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3090.301308] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3090.301310] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3090.301312] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3090.619067] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3090.619069] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3090.619071] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3090.619073] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3090.619075] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3090.619077] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3090.619078] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3090.619082] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3090.619083] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3090.619085] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3090.619087] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3090.619089] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3090.619090] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3090.619092] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3090.619094] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3090.619095] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3090.619097] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3090.619099] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3090.635574] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3090.635611] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3090.635633] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3090.635636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3090.635639] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3090.635641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3090.635643] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3090.635645] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3090.635647] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3090.635649] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3090.635652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3090.635654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3090.635656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3090.635658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3090.635660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3090.635662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3090.635664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3090.635666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3090.635668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3090.935951] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3090.935953] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3090.935955] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3090.935957] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3090.935959] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3090.935961] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3090.935963] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3090.935965] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3090.935967] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3090.935969] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3090.935971] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3090.935973] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3090.935975] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3090.935977] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3090.935979] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3090.935981] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3090.935983] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3090.935987] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3090.935990] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3090.935992] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3090.935993] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3090.935995] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3090.935997] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3090.935999] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3090.936001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3090.936003] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3090.936005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3090.936007] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3090.936008] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3090.936010] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3090.936012] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3090.936014] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3090.936016] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3090.936018] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3090.936020] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3090.936022] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3090.936024] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3090.936026] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3091.253596] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3091.253598] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3091.253600] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3091.253602] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3091.253604] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3091.253606] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3091.253608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3091.253610] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3091.253614] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3091.253617] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3091.253619] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3091.253620] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3091.253622] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3091.253624] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3091.253626] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3091.253628] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3091.253630] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3091.253632] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3091.253633] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3091.253635] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3091.253637] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3091.253639] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3091.253641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3091.253642] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3091.253644] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3091.253646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3091.253648] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3091.253650] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3091.253652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3091.253654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3091.253656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3091.253658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3091.253660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3091.253662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3091.253666] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3091.253667] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3091.253669] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3091.253671] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3091.253672] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3091.519926] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3091.519928] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3091.519930] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3091.519932] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3091.519936] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3091.519937] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3091.519939] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3091.519941] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3091.519943] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3091.519945] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3091.519946] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3091.519948] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3091.519950] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3091.519952] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3091.519953] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3091.536488] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3091.536551] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3091.536554] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3091.536557] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3091.536559] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3091.536562] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3091.536564] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3091.536566] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3091.536568] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3091.536571] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3091.536573] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3091.536575] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3091.536577] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3091.536579] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3091.536581] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3091.536583] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3091.536585] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3091.536587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3091.536589] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3091.536591] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3091.536593] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3091.536595] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3091.937072] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3091.937074] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3091.937076] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3091.937078] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3091.937080] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3091.937082] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3091.937086] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3091.937087] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3091.937089] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3091.937091] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3091.937092] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3091.937094] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3091.937096] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3091.937098] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3091.937099] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3091.937101] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3091.937103] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3091.953659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3091.953675] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3091.953678] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3091.953680] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3091.953682] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3091.953694] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3091.953696] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3091.953699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3091.953701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3091.953704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3091.953706] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3091.953708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3091.953711] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3091.953713] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3091.953715] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3091.953717] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3091.953719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3091.953721] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3091.953723] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3091.953725] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3092.237306] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3092.237308] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3092.237310] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3092.237312] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3092.237314] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3092.237320] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3092.237322] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3092.237324] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3092.237326] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3092.237328] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3092.237330] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3092.237332] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3092.237334] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3092.237336] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3092.237338] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3092.237340] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3092.237342] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3092.237344] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3092.237345] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3092.237347] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3092.237349] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3092.237351] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3092.237353] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3092.237355] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3092.237357] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3092.237359] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3092.237361] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3092.237363] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3092.237365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3092.237367] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3092.237369] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3092.237372] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3092.237374] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3092.237375] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3092.237377] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3092.237379] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3092.237381] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3092.237382] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3092.237384] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3092.237386] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3092.520951] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3092.520953] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3092.520955] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3092.520958] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3092.520960] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3092.520962] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3092.520963] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3092.520965] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3092.520967] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3092.520969] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3092.520970] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3092.520972] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3092.520974] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3092.520976] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3092.537464] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3092.537524] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3092.537527] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3092.537548] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3092.537551] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3092.537553] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3092.537556] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3092.537558] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3092.537560] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3092.537563] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3092.537565] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3092.537567] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3092.537569] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3092.537571] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3092.537573] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3092.537575] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3092.537577] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3092.537582] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3092.537585] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3092.537587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3092.537589] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3092.537591] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3092.537593] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3092.737753] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3092.737755] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3092.737762] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3092.737764] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3092.737766] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3092.737768] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3092.737773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3092.737775] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3092.737778] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3092.737780] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3092.737782] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3092.737785] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3092.737787] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3092.737789] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3092.737791] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3092.737793] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3092.737795] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3092.737796] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3092.737798] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3092.737800] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3092.737802] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3092.737804] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3092.737806] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3092.737807] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3092.737809] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3092.737811] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3092.737813] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3092.737815] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3092.737817] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3092.737819] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3092.737821] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3092.737823] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3092.737825] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3092.737827] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3092.737829] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3092.737831] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3092.737833] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3092.737835] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3093.060268] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3093.060270] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3093.060272] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3093.060274] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3093.060276] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3093.060278] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3093.060281] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3093.060283] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3093.060284] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3093.060286] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3093.060288] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3093.060290] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3093.060292] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3093.060294] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3093.060296] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3093.060297] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3093.060299] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3093.092579] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3093.092621] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3093.092638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3093.092654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3093.092656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3093.092659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3093.092674] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3093.092677] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3093.092697] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3093.092699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3093.092702] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3093.092707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3093.092709] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3093.092712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3093.418578] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3093.418579] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3093.418581] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3093.418583] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3093.418585] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3093.418587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3093.418589] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3093.418591] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3093.418592] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3093.418594] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3093.418596] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3093.418598] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3093.418600] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3093.418602] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3093.418604] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3093.418606] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3093.418608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3093.418610] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3093.418612] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3093.418614] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3093.418616] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3093.418618] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3093.418621] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3093.418622] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3093.418624] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3093.418626] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3093.418628] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3093.418629] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3093.418631] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3093.418633] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3093.418634] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3093.418636] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3093.418638] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3093.938601] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3093.938603] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3093.938605] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3093.938608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3093.938610] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3093.938612] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3093.938614] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3093.938616] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3093.938618] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3093.938620] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3093.938622] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3093.938624] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3093.938627] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3093.938628] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3093.938630] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3093.938632] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3093.938634] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3093.938635] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3093.938637] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3093.938639] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3093.938641] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3093.938642] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3093.938644] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3093.971116] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3093.971119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3093.971136] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3093.971138] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3093.971141] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3093.971143] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3093.971147] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3093.971149] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3094.306053] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3094.306055] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3094.306057] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3094.306058] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3094.306060] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3094.306062] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3094.306064] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3094.306065] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3094.306073] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3094.306074] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3094.306076] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3094.306078] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3094.306080] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3094.306082] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3094.306084] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3094.306085] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3094.306087] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3094.306089] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3094.306091] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3094.306093] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3094.306095] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3094.306097] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3094.306099] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3094.306101] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3094.306103] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3094.306105] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3094.306107] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3094.306109] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3094.306112] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3094.306114] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3094.306116] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3094.306117] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3094.306119] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3094.306121] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3094.306123] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3094.306124] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3094.306126] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3094.306128] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3094.306130] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3094.639733] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3094.639735] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3094.639737] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3094.639739] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3094.639741] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3094.639743] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3094.639745] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3094.639747] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3094.639750] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3094.639752] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3094.639753] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3094.639755] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3094.639757] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3094.639759] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3094.639760] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3094.639762] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3094.639764] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3094.639766] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3094.639767] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3094.639774] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3094.639779] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3094.639781] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3094.639783] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3094.639784] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3094.639786] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3094.639788] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3094.639790] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3094.639792] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3094.639794] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3094.639796] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3094.639798] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3094.639800] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3094.639801] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3094.639803] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3094.639805] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3094.639807] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3094.639809] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3094.639811] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3094.990049] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3094.990050] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3094.990054] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3094.990057] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3094.990058] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3094.990060] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3094.990062] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3094.990064] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3094.990069] mtk_tty_vuart: S][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3094.990071] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3094.990073] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3094.990075] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3094.990077] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3094.990078] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3094.990080] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3094.990082] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3094.990084] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3094.990086] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3094.990088] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3094.990090] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3094.990092] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3094.990094] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3094.990096] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3094.990098] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3094.990100] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3094.990102] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3094.990104] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3094.990106] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3094.990109] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3094.990110] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3094.990112] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3094.990114] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3094.990116] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3094.990117] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3094.990119] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3094.990121] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3094.990123] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3094.990124] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3094.990126] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3095.341175] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3095.341177] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3095.341179] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3095.341222] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3095.341225] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3095.341227] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3095.341229] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3095.341231] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3095.341234] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3095.341278] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3095.341281] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3095.341283] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 3770
[ 3095.341286] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3095.341288] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3095.341290] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3095.341292] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3095.341294] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3095.341339] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3095.341342] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3095.341343] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3095.341345] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3095.341347] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3095.341349] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3095.341390] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3095.341393] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3095.341395] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3095.341397] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3095.341399] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3095.341401] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3095.341402] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3095.341404] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3095.341446] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3095.341449] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3095.341451] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3095.341453] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3095.341455] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3095.341457] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3095.341503] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3096.016433] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3096.016435] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3096.016437] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3096.016438] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3096.016440] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3096.016442] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3096.016444] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3096.016445] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3096.016447] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3096.016449] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3096.016451] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3096.016459] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3096.016461] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3096.016463] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3096.016465] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3096.016467] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3096.016468] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3096.016470] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3096.016472] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3096.016474] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3096.016476] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3096.016478] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3096.016480] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3096.016482] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3096.016484] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3096.016486] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3096.016488] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3096.016490] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3096.016492] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3096.016494] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3096.016496] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 16398
[ 3096.016499] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3096.016501] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3096.016502] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3096.016504] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3096.016506] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3096.016508] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3096.016509] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3096.016511] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3096.016513] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3096.275201] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3096.275203] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3096.275205] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3096.275207] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3096.275209] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3096.275210] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3096.275212] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3096.275214] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3096.275218] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3096.275221] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3096.275223] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3096.275225] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3096.275227] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3096.275229] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3096.275232] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3096.275234] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3096.275236] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3096.275238] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3096.275240] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 20143
[ 3096.275243] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3096.275245] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3096.275246] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3096.275248] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3096.275250] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3096.275252] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3096.275253] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3096.275255] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3096.275257] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3096.275259] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3096.275261] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3096.291967] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3096.291991] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3096.291994] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3096.291996] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3096.291998] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3096.292000] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3096.292002] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3096.625323] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3096.625325] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3096.625327] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3096.625329] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 23681
[ 3096.625332] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3096.625334] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3096.625336] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3096.625337] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3096.625339] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3096.625341] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3096.625343] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3096.625344] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3096.625346] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3096.625348] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3096.625349] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3096.641897] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3096.641899] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3096.641928] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3096.641930] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3096.641932] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3096.641934] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3096.658773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3096.658776] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3096.658778] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3096.658781] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3096.658783] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3096.658785] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3096.658787] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3096.658789] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3096.658791] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3096.658793] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3096.658795] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3096.658797] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3096.658799] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3096.658801] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3096.658803] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3097.008940] mtk_tty_vuart: SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3097.008957] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3097.008960] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3097.008963] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3097.008965] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3097.008967] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3097.008969] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3097.008971] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3097.008973] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3097.008975] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3097.008977] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3097.008979] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3097.008981] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3097.008983] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3097.008985] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3097.008987] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3097.008989] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3097.008991] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3097.008993] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3097.008995] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3097.008997] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3097.008999] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3097.009001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3097.009003] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3097.009005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3097.009007] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3097.009009] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3097.009011] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 26449
[ 3097.009014] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3097.009016] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3097.009018] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3097.009020] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3097.009021] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3097.009023] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3097.009025] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3097.009031] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3097.009033] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3097.009035] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3097.009037] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3097.342673] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3097.342677] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3097.342679] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3097.342681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3097.342683] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3097.342686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 28220
[ 3097.342689] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3097.342691] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3097.342692] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3097.342694] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3097.342696] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3097.342698] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3097.342700] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3097.342701] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3097.342703] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3097.342705] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3097.342707] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3097.342715] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3097.342717] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3097.342727] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3097.342730] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3097.342731] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3097.342733] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3097.342735] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3097.342737] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3097.342739] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3097.342741] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3097.342742] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3097.342745] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3097.342747] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3097.342749] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3097.342751] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3097.342753] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3097.342755] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3097.342757] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3097.342759] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3097.342761] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3097.693371] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3097.693373] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3097.693375] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3097.693377] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3097.693379] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3097.693381] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3097.693383] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29548
[ 3097.693386] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3097.693388] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3097.693389] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3097.693391] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3097.693393] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3097.693395] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3097.693396] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3097.693398] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3097.693403] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3097.693405] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3097.693406] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3097.693415] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3097.693417] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3097.693419] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3097.693421] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3097.693423] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3097.693425] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3097.693426] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3097.693428] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3097.693430] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3097.693435] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3097.693437] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3097.693439] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3097.693441] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3097.693443] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3097.693445] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3097.693447] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3097.693449] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3097.693451] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3097.693453] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3097.693455] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3097.993325] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3097.993326] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3097.993328] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3097.993330] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3097.993332] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3097.993333] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3097.993335] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3097.993337] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3097.993338] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3097.993349] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3097.993351] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3097.993359] mtk_tty_vuart: ] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3097.993361] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3097.993363] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3097.993365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3097.993367] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3097.993368] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3097.993370] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3097.993372] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3097.993374] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3097.993376] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3097.993378] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3097.993380] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3097.993382] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3097.993384] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3097.993386] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3097.993388] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3097.993390] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3097.993392] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3097.993394] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3097.993396] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3097.993398] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3097.993401] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 30367
[ 3097.993404] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3097.993406] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3097.993407] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3097.993409] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3097.993411] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3097.993413] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3097.993415] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3098.343639] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3098.343641] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3098.343642] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3098.343644] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3098.343646] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3098.343648] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3098.343649] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3098.343651] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3098.343653] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3098.343662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3098.343664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3098.343665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3098.343671] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3098.343673] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3098.343675] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3098.343677] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3098.343678] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3098.343680] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3098.343682] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3098.343684] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3098.343686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3098.343688] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3098.343690] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3098.343692] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3098.343694] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3098.343696] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3098.343698] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3098.343700] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3098.343702] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3098.343704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3098.343706] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 31068
[ 3098.343709] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3098.343711] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3098.343712] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3098.343714] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3098.343716] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3098.343718] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3098.343719] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3098.343721] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3098.343723] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3098.660652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3098.660654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3098.660656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3098.660658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3098.660660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3098.660662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3098.660664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3098.660666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3098.660668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3098.660670] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3098.660672] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3098.660674] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 31522
[ 3098.660677] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3098.660679] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3098.660681] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3098.660682] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3098.660684] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3098.660686] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3098.660688] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3098.660689] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3098.660691] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3098.660693] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3098.660695] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3098.677235] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3098.677252] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3098.677255] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3098.677277] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3098.677280] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3098.677282] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3098.677284] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3098.677286] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3098.677289] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3098.677291] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3098.677293] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3098.677295] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3098.677296] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3098.677298] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3098.677300] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3099.011077] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3099.011079] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3099.011081] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3099.011082] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3099.011084] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3099.011086] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3099.011087] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3099.011089] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3099.011091] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3099.011093] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3099.027558] mtk_tty_vuart: S][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3099.027622] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3099.027625] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3099.027628] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3099.027630] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3099.027632] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3099.027634] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3099.027636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3099.027638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3099.027640] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3099.027642] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3099.027644] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3099.027646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3099.027648] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3099.027650] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3099.027652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3099.027654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3099.027656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3099.027659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3099.027660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3099.027662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3099.027665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3099.027666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3099.027668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3099.027671] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3099.027672] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 31898
[ 3099.027676] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3099.027677] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3099.027679] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3099.227921] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32059
[ 3099.227924] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3099.227926] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3099.227928] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3099.227930] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3099.227931] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3099.227933] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3099.227935] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3099.227936] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3099.227938] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3099.227940] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3099.227942] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3099.227951] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3099.227952] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3099.227954] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3099.227956] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3099.227961] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3099.227963] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3099.227965] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3099.227967] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3099.227969] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3099.227971] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3099.227973] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3099.227975] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3099.227977] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3099.227979] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3099.227981] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3099.227983] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3099.227985] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3099.227987] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3099.227989] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3099.227990] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3099.227992] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3099.227994] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3099.227997] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32059
[ 3099.228000] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3099.228001] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3099.228003] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3099.228005] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3099.694981] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3099.694983] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3099.694985] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3099.694987] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32246
[ 3099.694990] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3099.694992] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3099.694994] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3099.694995] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3099.694997] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3099.694999] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3099.695001] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3099.695002] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3099.695004] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3099.695006] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3099.695007] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3099.695020] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3099.695022] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3099.695024] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3099.695026] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3099.695027] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3099.695029] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3099.695031] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3099.695033] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3099.695035] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3099.695036] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3099.695038] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3099.695040] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3099.695042] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3099.695044] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3099.695046] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3099.695048] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3099.695050] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3099.695052] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3099.695054] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3099.695056] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3099.695058] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3099.695060] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3099.695062] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3099.695064] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32246
[ 3099.695067] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3099.695068] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3099.695070] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3099.695072] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3099.695074] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3099.695075] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3099.695077] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3099.695079] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3099.695081] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3099.695082] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3099.695084] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3099.711632] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3099.711636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3099.711638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3099.711641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3099.711643] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3099.711645] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3099.711647] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3099.711649] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3099.711651] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3099.711653] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3099.711655] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3099.711657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3099.711659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3099.711661] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3099.711662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3099.711664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3099.711666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3099.711668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3099.711670] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3099.711672] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3099.711674] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3099.711676] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3099.711678] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3099.711680] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3099.711682] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32255
[ 3099.711686] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3099.711687] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3099.962047] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3099.962049] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3099.962051] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32363
[ 3099.962054] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3099.962056] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3099.962058] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3099.962059] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3099.962061] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3099.962063] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3099.962065] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3099.962066] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3099.962068] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3099.962070] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3099.962071] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3099.978641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3099.978644] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3099.978647] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3099.978649] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3099.978651] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3099.978653] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3099.978655] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3099.978657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3099.978659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3099.978661] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3099.978663] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3099.978664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3099.978666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3099.978668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3099.978671] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3099.978673] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3099.978675] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3099.978677] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3099.978679] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3099.978681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3099.978691] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3099.978694] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3099.978696] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3099.978698] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32370
[ 3100.345644] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3100.345645] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3100.345647] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3100.345649] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3100.345657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3100.345659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3100.345661] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3100.345663] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3100.345665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3100.345667] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3100.345669] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3100.345670] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3100.345672] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3100.345674] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3100.345676] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3100.345678] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3100.345679] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3100.345681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3100.345683] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3100.345685] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3100.345687] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3100.345689] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3100.345691] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3100.345693] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3100.345695] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3100.345697] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3100.345699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3100.345701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3100.345703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32468
[ 3100.345706] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3100.345708] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3100.345710] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3100.345711] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3100.345713] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3100.345715] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3100.345716] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3100.345718] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3100.345720] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3100.345722] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3100.662633] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32546
[ 3100.662636] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3100.662638] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3100.662640] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3100.662642] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3100.662643] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3100.662645] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3100.662647] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3100.662649] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3100.662651] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3100.662653] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3100.662654] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3100.662663] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3100.662665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3100.662667] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3100.662669] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3100.662671] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3100.662673] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3100.662674] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3100.662676] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3100.662678] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3100.662680] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3100.662682] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3100.662683] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3100.662685] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3100.662687] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3100.662689] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3100.662691] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3100.662693] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3100.662695] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3100.662697] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3100.662699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3100.662701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3100.662703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3100.662705] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3100.662707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3100.662709] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32546
[ 3100.662712] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3100.662714] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3101.012999] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3101.013001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3101.013003] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3101.013005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3101.013007] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3101.013009] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3101.013013] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3101.013016] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3101.013018] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3101.013020] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3101.013022] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3101.013024] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3101.013025] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3101.013027] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3101.013029] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3101.013031] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3101.013033] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3101.013035] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3101.013037] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3101.013039] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3101.013041] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3101.013043] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3101.013045] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3101.013047] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3101.013049] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32608
[ 3101.013052] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3101.013054] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3101.013055] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3101.013057] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3101.013059] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3101.013061] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3101.013062] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3101.013064] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3101.013066] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3101.013068] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3101.013069] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3101.029551] mtk_tty_vuart: ][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3101.029607] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3101.029610] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3101.346706] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3101.346708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3101.346710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32647
[ 3101.346713] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3101.346715] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3101.346716] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3101.346718] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3101.346720] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3101.346722] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3101.346723] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3101.346725] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3101.346727] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3101.346729] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3101.346730] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3101.363274] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3101.363276] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3101.363279] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3101.363281] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3101.363283] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3101.363285] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3101.363287] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3101.363290] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3101.363292] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3101.363294] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3101.363296] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3101.363297] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3101.363300] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3101.363302] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3101.363304] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3101.363306] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3101.363308] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3101.363310] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3101.363312] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3101.363314] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3101.363316] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3101.363318] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32649
[ 3101.363321] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3101.363322] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3101.697028] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3101.697030] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3101.697031] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3101.697033] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3101.697035] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3101.697037] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3101.697038] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3101.697044] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3101.697046] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3101.697048] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3101.697050] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3101.697057] mtk_tty_vuart: DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3101.697059] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3101.697061] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3101.697063] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3101.697065] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3101.697067] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3101.697069] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3101.697071] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3101.697073] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3101.697075] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3101.697077] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3101.697078] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3101.697080] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3101.697082] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3101.697084] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3101.697086] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3101.697088] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3101.697090] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3101.697092] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3101.697094] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3101.697095] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3101.697097] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3101.697099] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3101.697101] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3101.697103] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3101.697105] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3101.697107] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32679
[ 3101.697111] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3102.031491] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3102.031547] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3102.031550] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3102.031572] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3102.031575] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3102.031577] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3102.031579] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3102.031581] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3102.031584] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3102.031586] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3102.031588] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3102.031590] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3102.031592] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3102.031594] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3102.031596] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3102.031598] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3102.031600] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3102.031603] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3102.031605] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3102.031607] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3102.031609] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3102.031611] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3102.031613] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3102.031617] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3102.031620] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3102.031622] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3102.031624] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3102.031626] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3102.031628] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3102.031630] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3102.031632] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3102.031634] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3102.031636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3102.031638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3102.330999] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3102.331001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32716
[ 3102.331004] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3102.331006] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3102.331008] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3102.331009] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3102.331011] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3102.331013] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3102.331015] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3102.331016] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3102.331018] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3102.331020] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3102.331022] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3102.363886] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3102.363904] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3102.363906] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3102.363922] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3102.363925] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3102.363941] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3102.363943] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3102.363958] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3102.363960] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3102.363962] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3102.363964] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3102.363980] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3102.363982] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3102.363984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3102.364003] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3102.364020] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3102.364022] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3102.364078] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3102.364081] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3102.364111] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3102.364113] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3102.364115] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3102.364117] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3102.364119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3102.364121] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32717
[ 3102.681319] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3102.681340] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3102.681344] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3102.681346] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3102.681348] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3102.681351] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3102.681354] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3102.681356] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3102.681359] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3102.681361] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3102.681363] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3102.681365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3102.681367] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3102.681399] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3102.681402] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3102.681404] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3102.681406] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3102.681408] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3102.681410] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3102.681452] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3102.681455] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3102.681457] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3102.681460] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3102.681462] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3102.681464] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3102.681510] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3102.681513] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3102.681515] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3102.681517] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3102.681519] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3102.681521] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3102.681522] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3102.681566] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3102.681568] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3102.681570] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3102.681572] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3102.681574] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3102.998248] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3102.998250] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3102.998252] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3102.998254] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3102.998299] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3102.998302] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3102.998304] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3102.998306] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3102.998308] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3102.998312] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3102.998314] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3102.998368] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3102.998374] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3102.998376] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3102.998378] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3102.998380] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3102.998382] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3102.998384] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3102.998385] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3102.998387] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3103.014620] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3103.014665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3103.014668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3103.014671] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3103.014673] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3103.014678] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3103.014701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3103.014703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3103.014705] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3103.014708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3103.014710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3103.014715] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3103.014718] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3103.014720] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3103.014722] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3103.014724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3103.014726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3103.348287] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3103.348289] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3103.348292] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3103.348311] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3103.348314] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3103.348316] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3103.348318] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3103.348320] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3103.348322] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3103.348368] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3103.348371] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3103.348374] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3103.348376] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3103.348378] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3103.348381] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3103.348457] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3103.348482] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3103.348485] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3103.348487] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3103.348490] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3103.348492] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3103.348496] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3103.348498] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3103.348501] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3103.348503] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3103.348505] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3103.348507] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3103.348509] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3103.348537] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3103.348540] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3103.348542] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3103.348544] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3103.348545] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3103.348547] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3103.348549] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3103.348551] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3103.348592] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3103.348595] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3103.665961] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3103.665963] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3103.665965] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3103.665968] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3103.665970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3103.666014] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3103.666017] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3103.666019] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3103.666021] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3103.666023] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3103.666027] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3103.666028] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3103.666082] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3103.666085] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3103.666086] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3103.666088] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3103.666090] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3103.666092] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3103.666094] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3103.666096] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3103.666098] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3103.682051] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3103.682066] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3103.682068] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3103.682071] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3103.682073] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3103.682075] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3103.682077] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3103.682079] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3103.682081] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3103.682083] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3103.682086] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3103.682088] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3103.682090] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3103.682092] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3103.682094] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3103.682096] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3104.016535] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3104.016538] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3104.016541] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3104.016543] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3104.016545] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3104.016547] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3104.016587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3104.016590] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3104.016592] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3104.016594] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3104.016596] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3104.016598] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3104.016642] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3104.016645] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3104.016647] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3104.016650] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3104.016652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3104.016654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3104.016697] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3104.016699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3104.016702] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3104.016704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3104.016706] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3104.016708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3104.016753] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3104.016756] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3104.016758] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3104.016760] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3104.016762] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3104.016765] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3104.016767] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3104.016810] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3104.016812] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3104.016814] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3104.016817] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3104.016818] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3104.016821] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3104.016823] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3104.332834] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3104.332836] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3104.332838] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3104.332840] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3104.332842] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3104.332844] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3104.332846] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3104.332848] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3104.332850] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3104.332852] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3104.332854] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3104.332858] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3104.332859] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3104.332861] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3104.332863] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3104.332864] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3104.332866] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3104.332868] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3104.332870] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3104.332871] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3104.332873] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3104.332875] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3104.349270] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3104.349283] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3104.349286] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3104.349288] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3104.349290] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3104.349295] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3104.349297] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3104.349299] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3104.349301] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3104.349320] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3104.349323] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3104.349327] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3104.349329] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3104.349331] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3104.349333] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3104.683230] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3104.683235] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3104.683238] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3104.683240] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3104.683242] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3104.683245] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3104.683247] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3104.683249] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3104.683251] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3104.683253] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3104.683255] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3104.683257] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3104.683259] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3104.683298] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3104.683301] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3104.683303] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3104.683305] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3104.683307] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3104.683309] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3104.683346] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3104.683349] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3104.683351] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3104.683353] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3104.683355] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3104.683357] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3104.683359] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3104.683395] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3104.683397] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3104.683399] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3104.683401] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3104.683403] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3104.683405] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3104.683406] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3104.683408] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3104.683450] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3104.683453] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3104.683455] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3104.683457] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3105.000246] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3105.000248] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3105.000250] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3105.000252] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3105.000297] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3105.000299] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3105.000301] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3105.000304] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3105.000306] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3105.000310] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3105.000311] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3105.000367] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3105.000369] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3105.000371] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3105.000373] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3105.000374] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3105.000376] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3105.000378] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3105.000380] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3105.000382] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3105.016625] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3105.016665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3105.016681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3105.016684] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3105.016705] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3105.016708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3105.016711] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3105.016713] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3105.016716] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3105.016718] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3105.016720] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3105.016722] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3105.016724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3105.016726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3105.016728] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3105.016730] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3105.016733] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3105.350710] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3105.350712] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3105.367068] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3105.367103] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3105.367106] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3105.367108] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3105.367111] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3105.367113] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3105.367115] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3105.367117] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3105.367120] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3105.367122] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3105.367124] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3105.367127] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3105.367129] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3105.367131] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3105.367133] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3105.367135] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3105.367137] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3105.367139] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3105.367141] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3105.367143] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3105.367145] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3105.367147] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3105.367149] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3105.367151] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3105.367153] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3105.367155] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3105.367157] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3105.367159] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3105.367161] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3105.367163] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3105.367165] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3105.367167] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3105.367169] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3105.670455] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3105.670457] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3105.670502] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3105.670504] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3105.670506] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3105.670509] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3105.670511] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3105.670515] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3105.670516] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3105.670572] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3105.670574] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3105.670576] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3105.670578] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3105.670580] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3105.670582] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3105.670584] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3105.670586] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3105.670587] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3105.701491] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3105.701504] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3105.701507] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3105.701528] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3105.701531] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3105.701535] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3105.701538] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3105.701541] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3105.701543] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3105.701545] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3105.701548] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3105.701579] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3105.701582] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3106.030840] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3106.030845] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3106.030848] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3106.030850] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3106.030853] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3106.030855] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3106.030858] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3106.030893] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3106.030896] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3106.030898] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3106.030900] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3106.030903] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3106.030905] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3106.030949] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3106.030952] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3106.030954] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3106.030956] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3106.030959] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3106.030961] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3106.031005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3106.031008] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3106.031010] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3106.031013] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3106.031015] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3106.031017] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3106.031061] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3106.031064] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3106.031066] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3106.031068] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3106.031070] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3106.031072] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3106.031074] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3106.031119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3106.031122] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3106.031123] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3106.031136] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3106.031138] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3106.334755] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3106.334757] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3106.334759] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3106.334761] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3106.334763] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3106.334765] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3106.334767] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3106.334769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3106.334771] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3106.334773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3106.334775] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3106.334778] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3106.334780] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3106.334781] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3106.334783] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3106.334785] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3106.334787] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3106.334788] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3106.334790] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3106.334792] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3106.334794] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3106.334795] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3106.351277] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3106.351290] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3106.351292] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3106.351295] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3106.351297] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3106.351301] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3106.351303] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3106.351305] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3106.351308] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3106.351310] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3106.351328] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3106.351332] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3106.351335] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3106.351337] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3106.351339] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3106.685018] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3106.685020] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3106.685041] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3106.685044] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3106.685046] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3106.685048] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3106.685051] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3106.685053] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3106.685055] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3106.685057] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3106.685059] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3106.685061] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3106.685063] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3106.685065] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3106.685067] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3106.685069] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3106.685071] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3106.685073] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3106.685076] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3106.685078] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3106.685080] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3106.685082] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3106.685084] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3106.685086] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3106.685088] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3106.685090] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3106.685092] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3106.685097] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3106.685099] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3106.685101] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3106.685103] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3106.685105] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3106.685107] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3106.685109] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3106.685111] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3106.685112] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3106.685114] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3107.018616] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3107.018621] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3107.018623] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3107.018625] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3107.018628] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3107.018646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3107.018648] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3107.018652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3107.018654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3107.018656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3107.018658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3107.018661] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3107.018663] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3107.018704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3107.018707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3107.018709] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3107.018711] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3107.018713] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3107.018715] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3107.018766] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3107.018769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3107.018771] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3107.018773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3107.018776] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3107.018778] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3107.018817] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3107.018819] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3107.018821] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3107.018824] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3107.018826] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3107.018828] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3107.018830] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3107.018874] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3107.018876] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3107.018878] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3107.018880] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3107.018882] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3107.018884] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3107.336306] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3107.336308] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3107.336310] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3107.336312] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3107.336314] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3107.336358] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3107.336360] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3107.336362] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3107.336365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3107.336367] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3107.336370] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3107.336371] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3107.336429] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3107.336431] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3107.336433] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3107.336435] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3107.336437] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3107.336439] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3107.336440] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3107.336442] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3107.336444] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3107.352504] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3107.352507] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3107.352509] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3107.352512] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3107.352514] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3107.352547] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3107.352550] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3107.352552] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3107.352555] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3107.352557] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3107.352559] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3107.352561] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3107.352603] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3107.352605] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3107.352607] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3107.352609] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3107.352611] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3107.691522] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3107.691528] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3107.691530] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3107.691533] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3107.691535] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3107.691537] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3107.691539] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3107.691565] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3107.691568] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3107.691570] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3107.691572] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3107.691575] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3107.691576] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3107.691617] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3107.691620] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3107.691622] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3107.691625] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3107.691627] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3107.691629] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3107.691673] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3107.691675] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3107.691678] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3107.691686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3107.691689] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3107.691691] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3107.691729] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3107.691732] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3107.691734] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3107.691736] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3107.691738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3107.691739] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3107.691741] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3107.691785] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3107.691787] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3107.691789] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3107.691791] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3107.691792] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3107.691795] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3108.003219] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3108.003221] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3108.003266] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3108.003268] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3108.003271] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3108.003273] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3108.003275] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3108.003281] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3108.003344] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3108.020247] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3108.020249] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3108.020251] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3108.020260] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3108.020262] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3108.020272] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3108.020274] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3108.020277] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3108.020285] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3108.020287] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3108.020289] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3108.020299] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3108.020301] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3108.020307] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3108.020309] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3108.020311] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3108.020324] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3108.020325] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3108.020327] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3108.020335] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3108.020341] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3108.020342] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3108.020344] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3108.020348] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3108.020351] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3108.020353] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3108.020355] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3108.353353] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3108.353357] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3108.353360] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3108.353362] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3108.353364] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3108.353366] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3108.353368] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3108.353412] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3108.353416] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3108.353419] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3108.353421] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3108.353423] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3108.353425] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3108.353469] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3108.353472] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3108.353474] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3108.353476] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3108.353479] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3108.353481] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3108.353527] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3108.353531] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3108.353533] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3108.353535] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3108.353537] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3108.353539] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3108.353541] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3108.353582] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3108.353585] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3108.353587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3108.353589] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3108.353591] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3108.353593] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3108.353595] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3108.353597] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3108.353639] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3108.353643] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3108.353645] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3108.353647] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3108.688679] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3108.688681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3108.688683] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3108.688685] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3108.688687] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3108.688689] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3108.688691] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3108.688693] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3108.688695] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3108.688697] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3108.688699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3108.688701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3108.688703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3108.688705] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3108.688707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3108.688708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3108.688710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3108.688712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3108.688714] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3108.688716] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3108.688718] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3108.688719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3108.688721] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3108.688723] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3108.688725] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3108.688727] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3108.688729] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3108.688731] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3108.688733] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3108.688735] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3108.688737] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3108.688738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3108.688743] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3108.688745] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3108.688747] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3108.688749] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3108.688753] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3108.688781] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3109.020622] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3109.020625] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3109.020627] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3109.020629] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3109.020649] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3109.020651] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3109.020653] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3109.020655] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3109.020658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3109.020660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3109.020665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3109.020668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3109.020670] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3109.020672] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3109.020675] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3109.020677] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3109.020706] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3109.020709] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3109.020712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3109.020714] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3109.020716] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3109.020718] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3109.020761] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3109.020764] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3109.020766] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3109.020768] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3109.020770] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3109.020772] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3109.020989] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3109.020993] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3109.020995] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3109.020997] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3109.020999] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3109.021001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3109.021004] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3109.021006] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3109.021007] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3109.187946] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3109.187948] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3109.187950] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3109.187952] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3109.187953] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3109.187955] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3109.204133] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3109.204189] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3109.204192] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3109.204194] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3109.204216] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3109.204218] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3109.204221] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3109.204223] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3109.204226] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3109.204228] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3109.204230] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3109.204232] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3109.204235] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3109.204236] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3109.204238] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3109.204241] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3109.204243] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3109.204245] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3109.204247] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3109.204249] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3109.204251] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3109.204253] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3109.204255] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3109.204257] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3109.204259] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3109.204261] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3109.204262] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3109.204264] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3109.204266] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3109.204271] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3109.465140] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3109.465143] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3109.465146] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3109.465148] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3109.465151] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3109.465184] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3109.465188] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3109.465190] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3109.465192] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3109.465194] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3109.465196] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3109.465198] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3109.465239] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3109.465242] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3109.465244] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3109.465246] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3109.465248] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3109.465250] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3109.465252] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3109.465253] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3109.465295] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3109.465298] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3109.465300] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3109.465303] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3109.465305] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3109.465307] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3109.465350] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3109.465353] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3109.465355] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3109.465358] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3109.465360] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3109.789052] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3109.789054] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3109.789098] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3109.789100] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3109.789103] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3109.789105] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3109.789107] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3109.789109] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3109.789153] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3109.789155] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3109.789157] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3109.789159] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3109.789162] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3109.789164] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3109.789165] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3109.789210] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3109.789212] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3109.789214] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3109.789216] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3109.789218] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3109.789220] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3109.789222] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3109.789224] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3109.789263] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3109.789266] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3109.789268] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3109.789270] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3109.789273] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3109.789275] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3109.789320] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3109.789323] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3109.789325] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3109.789327] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3109.789329] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3109.789333] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3109.789335] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3109.789389] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3109.789391] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3109.789393] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3109.789395] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3110.115704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3110.115707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3110.115710] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3110.115712] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3110.115768] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3110.115770] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3110.115772] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3110.115774] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3110.115776] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3110.115778] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3110.115780] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3110.115781] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3110.115783] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3110.146495] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3110.146548] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3110.146551] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3110.146553] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3110.146556] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3110.146558] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3110.146560] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3110.146562] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3110.146564] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3110.146566] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3110.146568] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3110.146570] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3110.146573] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3110.146574] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3110.146576] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3110.146578] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3110.146580] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3110.146586] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3110.146588] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3110.146590] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3110.146593] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3110.146595] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3110.146597] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3110.146599] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3110.472498] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3110.472500] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3110.472503] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3110.472505] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3110.472507] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3110.472509] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3110.472554] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3110.472556] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3110.472558] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3110.472560] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3110.472563] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3110.472564] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3110.472566] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3110.472609] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3110.472612] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3110.472614] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3110.472616] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3110.472628] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3110.472630] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3110.472632] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3110.472634] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3110.472669] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3110.472672] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3110.472674] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3110.472677] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3110.472679] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3110.472681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3110.472726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3110.472728] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3110.472730] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3110.472733] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3110.472735] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3110.472739] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3110.472741] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3110.472793] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3110.472795] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3110.472797] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3110.472799] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3110.472801] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3110.472803] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3110.803779] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3110.803781] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3110.803785] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3110.803786] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3110.803788] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3110.803790] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3110.803792] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3110.803793] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3110.803795] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3110.803797] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3110.803799] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3110.803801] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3110.803802] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3110.829915] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3110.829929] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3110.829931] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3110.829934] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3110.829936] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3110.829959] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3110.829962] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3110.829964] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3110.829966] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3110.829968] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3110.829970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3110.830011] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3110.830014] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3110.830016] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3110.830018] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3110.830021] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3110.830023] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3110.830065] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3110.830068] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3110.830070] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3110.830072] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3110.830074] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3110.830076] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3110.830123] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3111.123240] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3111.123242] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3111.123244] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3111.123290] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3111.123293] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3111.123295] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3111.123298] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3111.123300] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3111.123303] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3111.123305] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3111.123360] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3111.123363] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3111.123365] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3111.123367] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3111.123369] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3111.123371] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3111.123373] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3111.123374] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3111.123376] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3111.155399] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3111.155411] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3111.155414] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3111.155417] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3111.155419] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3111.155441] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3111.155444] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3111.155446] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3111.155449] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3111.155451] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3111.155453] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3111.155495] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3111.448330] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3111.453996] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3111.454006] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3111.454007] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3111.454010] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3111.454011] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3111.454013] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3111.454015] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3111.454017] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3111.454018] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3111.454020] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3111.480500] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3111.480517] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3111.480520] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3111.480540] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3111.480543] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3111.480548] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3111.480551] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3111.480553] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3111.480555] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3111.480558] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3111.480560] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3111.480596] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3111.480599] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3111.480601] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3111.480603] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3111.480605] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3111.480608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3111.480652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3111.480655] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3111.480657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3111.806572] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3111.806574] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3111.806576] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3111.806579] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3111.806582] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3111.806584] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3111.806630] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3111.806633] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3111.806634] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3111.806636] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3111.806638] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3111.806640] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3111.806642] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3111.806644] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3111.806646] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3111.838686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3111.838726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3111.838742] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3111.838745] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3111.838761] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3111.838778] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3111.838781] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3111.838783] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3111.838805] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3111.838807] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3111.838810] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3111.838815] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3111.838817] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3111.838820] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3111.838822] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3111.838824] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3112.131658] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3112.131660] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3112.131662] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3112.131664] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3112.131665] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3112.164078] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3112.164091] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3112.164094] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3112.164096] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3112.164098] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3112.164122] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3112.164124] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3112.164127] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3112.164129] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3112.164132] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3112.164134] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3112.164175] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3112.164178] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3112.164180] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3112.164182] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3112.164184] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3112.164186] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3112.164237] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3112.164240] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3112.164243] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3112.164245] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3112.164247] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3112.164250] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3112.164288] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3112.164291] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3112.620808] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3112.653557] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3112.653588] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3112.653591] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3112.653594] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3112.653596] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3112.653601] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3112.653604] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3112.653606] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3112.653608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3112.653610] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3112.653613] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3112.653644] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3112.653647] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3112.653649] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3112.653651] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3112.653654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3112.653656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3112.653699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3112.653703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3112.653705] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3112.653707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3112.653709] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3112.653711] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3112.653754] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3112.653756] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3112.653758] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3112.653761] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3113.041387] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3113.041389] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3113.041391] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3113.041393] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3113.041436] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3113.041438] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3113.041440] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3113.041443] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3113.041445] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3113.041447] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3113.041491] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3113.041494] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3113.041496] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3113.041498] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3113.041500] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3113.041502] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3113.041503] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3113.041548] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3113.041550] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3113.041552] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3113.041555] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3113.041557] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3113.041559] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3113.041560] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3113.041562] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3113.041604] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3113.041606] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3113.041608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3113.041611] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3113.041613] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3113.041615] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3113.041660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3113.041662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3113.041664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3113.041666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3113.041669] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3113.041673] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3113.041675] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3113.356500] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3113.356502] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3113.356504] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3113.356506] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3113.356509] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3113.356511] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3113.356513] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3113.356515] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3113.356517] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3113.356519] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3113.356521] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3113.356523] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3113.356525] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3113.356526] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3113.356528] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3113.356530] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3113.356532] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3113.356534] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3113.356536] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3113.356538] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3113.356539] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3113.356541] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3113.356543] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3113.356545] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3113.356547] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3113.356549] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3113.356551] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3113.356553] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3113.356555] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3113.356557] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3113.356559] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3113.356560] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3113.356562] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3113.356564] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3113.356566] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3113.356570] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3113.356571] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3113.356573] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3113.356575] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3113.692341] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3113.692343] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3113.692345] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3113.692346] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] con^C
130|console:/ #

#----------------------------------------------------------
# Sample TV Configuration IDK 5.x VSVDB V2 HDMI Interface 0x3
# Feature(PQ) : Precision Detail, Light Sense, 6-Vector
# 07/07/2023								
#---------------------------------------------------------- 

[Global]
# Panel Specific
Tmax = 338
Tmin = 0.05 0.001 # one additional element
TPrimaries = 0.6657 0.3151 0.2661 0.6462 0.1547 0.0544 0.3127 0.3290 0.005 # one additional element
TEOTF = POWER
Tgamma = 2.2
XXX = 1.00 # undef parameter

# System-specific 
RGB2YCCmat = 0.18191707 0.611980659 0.061779927 -0.100275074 -0.337331764 0.437606838 0.437606838 -0.397480836 -0.040126002
RGB2YCCOffsetNorm = 0.0625 0.5 0.5
DMSwVersion = 4.0
IDK_Version = SOC_5.1

# Picture mode index parameters
ReferenceDarkPicModeIndex = 1
DoViBrightPicModeIndex = 0

# L1L4 metadata generation
enableL1L4gen = 1

# Light Sense
Ambient = 1
TFrontLux = 100
TRearLum = 5
TWhitexy = 0.3127 0.3290
TSurroundReflection = 0.05
TScreenReflection = 0.0001
TFrontLuxScale = 0.31
TRearLumScale = 1.0
ALDelayMilliSec = 0
ALResponseRise = 0.0208     # 2[sec] response : 0.0208 = 1/48 = 1/(24*sec)
ALResponseFall = 0.0208     # 2[sec] response : 0.0208 = 1/48
AmbientFrontLux   = 50 100 200 500 800 1000 2000 3000
AmbientCompLevel  = 1.000000 1.000000 1.000000 1.000000 1.000000 1.000000 1.000000 1.000000

# For AI SDK
# Precision Detail
SupportsPrecisionRendering = 1
PrecisionRenderingStrength = 0.75
PrecisionRendering29Scalar = 0.75
DBrightness_PR_on = 0.35
DLocalContrast = 0.35

#6 vectors
L8SatVecOffsets = 0 0 0 0 0 0
L8HueVecOffsets = 0 0 0 0 0 0

# VSVDB Related Information
vsvdb_version = 2
vsvdb_dm_version = 4.0_VSEM                       # Set 2.9, 3.1, 4.0 or 4.0_VSEM
#vsvdb_Tmax = 338
#vsvdb_Tmin = 0.05
#vsvdb_TPrimaries = 0.6690 0.3135 0.2655 0.6408 0.1514 0.0521 0.3127 0.3290
support_normal_dolbyvision = 1                    # Sink-Led Switch, 0 = DISABLE, 1 = ENABLE
support_lowlatency_v1_12b = 0                     # Enable for VSVDB V1 with 12byte Source-Led
#support_lowlatency_yuv_dolbyvision = 1           # NOT FOR VSVDB V2
support_lowlatency_rgb_dolbyvision = 1            # Source-Led RGB and Source-Led with BT.2020 Color Primary Switch
support_12b_yuv_422 = 1
support_12b_yuv_rgb_444 = 0
support_10b_yuv_rgb_444 = 0
#support_2160P60 = 0                              # NOT FOR VSVDB V2
support_dolby_vision_gaming = 0                  # 0 = ENABLE, 1 = DISABLE
vsvdb_parity = 1

# Tuning for each Picture Mode
[PictureMode = 0]
PictureModeName = IQ
# Picture Quality Preference
DBrightness = 0.3
Tmin = 0.05
# Light Sense
Ambient = 1
# Precision Detail
SupportsPrecisionRendering = 1
PrecisionRenderingStrength = 0.75
PrecisionRendering29Scalar = 0.75
DBrightness_PR_on = 0.35 			# making it slightly brighter when precision detail is on
DLocalContrast = 0.35 				# set to higher if more aggressive sharpness is preferred
#6 vectors for color enhancement
L8SatVecOffsets = 0.1 0 0.1 0 0 0
L8HueVecOffsets = 0 0 0 0 0 0


[PictureMode = 1]
PictureModeName = Dark
DBrightness = 0.0
# Light Sense
Ambient = 1
TFrontLux = 0
# Precision Detail
SupportsPrecisionRendering = 0



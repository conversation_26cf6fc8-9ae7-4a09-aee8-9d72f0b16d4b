
130|console:/ #
130|console:/ # su
ys/bus/rpmsg/devices/virtio*.pqu-vuart.-*/mtk_dbg/record_log                  <
console:/ # dmesg -w | grep DolbyDBG
[ 3262.907976] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3262.907978] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3262.907980] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3262.907982] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3262.907983] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3262.907985] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3262.907987] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3262.907989] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3262.907990] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3262.907992] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3262.924566] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3262.924623] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3262.924626] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3262.924629] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3262.924631] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3262.924633] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3262.924635] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3262.924638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3262.924640] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3262.924642] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3262.924645] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3262.924646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3262.924648] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3262.924651] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3262.924653] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3262.924655] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3262.924657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3262.924659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3262.924661] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3262.924663] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3262.924665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3262.924667] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3262.924669] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3262.924671] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3262.924672] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3262.924674] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3262.924676] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3262.924678] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3262.924680] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3262.924682] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3262.924684] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3262.924689] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3262.924691] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3262.924693] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3262.924695] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3262.924697] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3262.924699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3262.924700] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3262.924703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3262.924704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3262.924706] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3262.924708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3262.924710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3262.924712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3262.924714] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3262.924716] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3262.924718] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3262.924720] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3262.924722] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3262.924724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3262.924726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3262.924728] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3262.924730] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3262.924732] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3262.924734] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3262.924737] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3262.924739] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3262.924741] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3262.924743] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3262.924744] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3262.924746] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3262.924748] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3262.924750] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3262.924751] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3262.924753] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3262.924755] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3262.941397] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3262.941461] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3262.941464] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3262.941467] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3262.941469] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3262.941472] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3262.941474] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3262.941476] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3262.941479] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3262.941481] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3262.941483] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3262.941485] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3262.941487] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3262.941489] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3262.941491] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3262.941493] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3262.941495] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3262.941497] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3262.941499] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3262.941501] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3262.941503] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3262.941505] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3262.941507] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3262.941509] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3262.941511] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3262.941513] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3262.941515] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3262.941517] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3262.941519] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3262.941521] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3262.941522] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3262.941524] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3262.941526] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3262.941528] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3262.941530] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3262.941532] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3262.941537] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3262.941539] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3262.941541] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3262.941542] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3262.941544] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3262.941546] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3262.941548] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3262.941550] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3262.941552] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3262.941554] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3262.941556] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3262.941558] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3262.941560] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3262.941562] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3262.941564] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3262.941565] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3262.941567] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3262.941569] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3262.941571] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3262.941575] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3262.941576] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3262.941578] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3262.941580] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3262.941582] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3262.941583] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3262.941585] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3262.941587] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3262.941589] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3262.941590] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3262.941592] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3262.971517] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3262.971581] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3262.971583] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3262.971586] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3262.971588] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3262.971590] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3262.971592] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3262.971595] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3262.971597] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3262.971599] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3262.971601] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3262.971603] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3262.971605] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3262.971608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3262.971610] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3262.971612] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3262.971614] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3262.971616] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3262.971618] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3262.971620] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3262.971622] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3262.971624] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3262.971626] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3262.971628] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3262.971630] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3262.971632] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3262.971634] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3262.971635] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3262.971637] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3262.971642] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3262.971644] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3262.971646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3262.971648] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3262.971650] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3262.971652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3262.971654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3262.971656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3262.971658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3262.971659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3262.971661] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3262.971663] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3262.971665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3262.971667] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3262.971669] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3262.971670] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3262.971672] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3262.971674] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3262.971676] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3262.971678] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3262.971685] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3262.971687] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3262.971689] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3262.971691] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3262.971693] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3262.971695] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3262.971699] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3262.971701] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3262.971702] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3262.971704] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3262.971706] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3262.971708] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3262.971709] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3262.971711] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3262.971713] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3262.971715] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3262.971716] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3262.978381] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3262.978418] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3262.978440] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3262.978443] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3262.978445] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3262.978447] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3262.978449] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3262.978452] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3262.978454] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3262.978456] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3262.978458] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3262.978460] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3262.978463] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3262.978465] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3262.978467] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3262.978469] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3262.978472] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3262.978474] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3262.978476] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3262.978478] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3262.978480] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3262.978482] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3262.978484] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3262.978489] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3262.978491] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3262.978493] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3262.978495] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3262.978497] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3262.978499] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3262.978501] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3262.978503] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3262.978505] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3262.978507] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3262.978509] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3262.978511] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3262.978513] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3262.978514] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3262.978516] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3262.978518] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3262.978520] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3262.978522] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3262.978524] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3262.978526] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3262.978527] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3262.978529] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3262.978531] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3262.978533] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3262.978535] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3262.978537] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3262.978539] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3262.978541] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3262.978543] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3262.978545] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3262.978547] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3262.978549] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3262.978552] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3262.978554] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3262.978556] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3262.978557] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3262.978559] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3262.978561] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3262.978563] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3262.978564] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3262.978566] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3262.978568] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3262.978569] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3263.008943] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.008982] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.008999] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.009001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.009024] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.009027] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.009029] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.009031] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.009034] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.009036] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.009039] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.009041] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.009043] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.009045] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.009047] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.009049] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.009051] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.009053] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.009055] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.009057] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.009059] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.009061] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.009063] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.009065] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.009067] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.009069] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.009071] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.009073] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.009075] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.009079] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.009082] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3263.009084] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3263.009086] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3263.009088] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3263.009089] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3263.009091] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3263.009093] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3263.009095] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3263.009097] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3263.009099] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3263.009100] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3263.009102] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3263.009104] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3263.009106] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3263.009108] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.009110] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.009112] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.009114] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.009116] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.009118] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.009120] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.009122] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.009124] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.009126] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.009128] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3263.009131] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3263.009133] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3263.009134] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3263.009136] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3263.009138] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3263.009140] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3263.009141] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3263.009143] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3263.009145] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3263.009147] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3263.009148] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3263.024547] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.024604] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.024621] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.024623] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.024646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.024649] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.024651] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.024653] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.024656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.024658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.024660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.024662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.024664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.024666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.024668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.024670] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.024672] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.024677] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.024680] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.024682] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.024684] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.024686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.024688] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.024690] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.024692] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.024694] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.024697] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.024699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.024701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.024703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.024705] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3263.024707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3263.024708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3263.024710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3263.024712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3263.024714] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3263.024716] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3263.024718] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3263.024719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3263.024721] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3263.024723] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3263.024725] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3263.024727] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3263.024729] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3263.024730] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.024732] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.024734] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.024736] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.024738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.024740] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.024742] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.024744] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.024746] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.024748] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.024750] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3263.024754] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3263.024755] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3263.024757] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3263.024759] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3263.024761] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3263.024762] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3263.024764] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3263.024766] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3263.024767] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3263.024769] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3263.024771] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3263.041386] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.041439] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.041442] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.041444] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.041446] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.041449] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.041451] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.041453] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.041455] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.041458] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.041460] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.041462] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.041464] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.041466] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.041468] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.041470] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.041472] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.041474] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.041476] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.041478] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.041480] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.041482] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.041484] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.041485] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.041487] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.041489] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.041491] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.041493] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.041495] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.041497] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.041500] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3263.041501] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3263.041503] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3263.041505] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3263.041507] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3263.041509] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3263.041513] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3263.041516] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3263.041517] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3263.041519] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3263.041521] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3263.041523] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3263.041525] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3263.041527] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3263.041529] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.041531] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.041533] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.041535] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.041537] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.041539] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.041541] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.041543] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.041545] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.041547] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.041549] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3263.041552] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3263.041554] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3263.041556] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3263.041558] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3263.041559] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3263.041561] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3263.041563] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3263.041565] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3263.041566] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3263.041568] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3263.041570] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3263.058000] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.058039] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.058061] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.058064] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.058066] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.058069] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.058071] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.058073] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.058075] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.058077] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.058079] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.058081] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.058084] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.058086] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.058088] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.058090] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.058092] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.058094] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.058096] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.058098] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.058100] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.058102] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.058104] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.058106] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.058108] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.058110] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.058112] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.058114] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.058116] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.058118] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.058120] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3263.058122] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3263.058124] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3263.058125] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3263.058127] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3263.058129] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3263.058134] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3263.058136] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3263.058138] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3263.058140] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3263.058142] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3263.058143] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3263.058145] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3263.058147] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3263.058149] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.058151] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.058153] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.058155] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.058157] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.058159] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.058161] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.058163] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.058165] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.058167] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.058169] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3263.058172] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3263.058174] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3263.058176] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3263.058177] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3263.058179] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3263.058181] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3263.058183] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3263.058184] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3263.058186] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3263.058188] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3263.058190] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3263.074704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.074766] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.074769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.074772] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.074774] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.074776] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.074778] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.074781] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.074783] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.074785] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.074787] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.074789] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.074791] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.074793] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.074796] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.074798] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.074800] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.074802] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.074804] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.074806] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.074808] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.074810] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.074812] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.074814] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.074815] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.074817] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.074819] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.074821] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.074823] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.074828] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.074831] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3263.074833] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3263.074835] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3263.074837] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3263.074839] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3263.074841] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3263.074843] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3263.074845] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3263.074846] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3263.074848] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3263.074850] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3263.074852] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3263.074854] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3263.074856] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3263.074857] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.074859] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.074862] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.074864] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.074865] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.074868] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.074870] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.074872] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.074874] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.074876] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.074878] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3263.074881] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3263.074883] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3263.074885] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3263.074887] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3263.074888] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3263.074890] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3263.074892] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3263.074894] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3263.074895] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3263.074897] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3263.074899] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3263.091350] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.091415] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.091418] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.091420] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.091422] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.091425] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.091427] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.091429] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.091431] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.091434] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.091436] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.091438] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.091440] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.091442] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.091445] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.091447] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.091449] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.091451] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.091453] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.091455] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.091457] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.091459] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.091461] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.091495] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.091498] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.091500] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.091502] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.091504] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.091506] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.091511] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.091514] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3263.091516] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3263.091518] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3263.091520] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3263.091522] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3263.091524] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3263.091526] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3263.091527] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3263.091529] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3263.241801] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3263.241802] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3263.241804] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3263.241806] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3263.241808] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3263.241810] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3263.241812] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3263.241814] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.241817] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.241819] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.241821] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.241823] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.241825] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.241827] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.241829] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.241831] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.241833] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.241835] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3263.241838] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3263.241840] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3263.241842] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3263.241844] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3263.241845] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3263.241847] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3263.241849] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3263.241850] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3263.241852] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3263.241854] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3263.241856] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3263.258250] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.258297] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.258300] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.258302] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.258305] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.258307] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.258309] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.258312] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.258314] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.258317] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.558629] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.558631] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.558633] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.558636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3263.558638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3263.558640] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3263.558641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3263.558643] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3263.558645] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3263.558647] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3263.558649] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3263.558651] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3263.558652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3263.558654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3263.558656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3263.558658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3263.558660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3263.558662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.558663] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.558666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.558667] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.558669] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.558671] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.558673] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.558675] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.558677] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.558679] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.558681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3263.558685] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3263.558686] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3263.558688] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3263.558690] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3263.558691] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3263.558693] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3263.558695] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3263.558697] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3263.558699] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3263.558700] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3263.558702] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3263.758848] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.758850] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.758856] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.758858] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.758860] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3263.758864] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3263.758865] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3263.758867] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3263.758869] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3263.758870] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3263.758872] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3263.758874] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3263.758876] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3263.758877] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3263.758879] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3263.758881] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3263.775420] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.775482] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.775485] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.775487] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.775490] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.775492] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.775494] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.775496] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.775499] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.775501] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.775504] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3263.775506] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3263.775508] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3263.775510] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3263.775513] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3263.775515] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3263.775517] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3263.775519] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3263.775521] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3263.775523] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3263.775525] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3264.035683] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3264.035685] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3264.035687] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3264.035689] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3264.035691] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3264.035693] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3264.035695] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3264.035697] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3264.035699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3264.035701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3264.035703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3264.035706] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3264.035708] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3264.035709] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3264.035711] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3264.035713] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3264.035715] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3264.035716] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3264.035718] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3264.035720] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3264.035721] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3264.035723] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3264.067154] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3264.067167] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3264.067169] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3264.067172] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3264.067174] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3264.067220] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3264.067254] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3264.067257] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3264.067273] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3264.067276] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3264.067278] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3264.067296] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3264.425587] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3264.425589] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3264.425590] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3264.425592] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3264.425594] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3264.425596] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3264.425597] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3264.457923] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3264.457978] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3264.457982] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3264.457999] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3264.458001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3264.458004] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3264.458025] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3264.458027] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3264.458030] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3264.458032] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3264.458035] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3264.458037] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3264.458039] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3264.458041] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3264.458044] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3264.458046] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3264.458048] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3264.458053] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3264.458055] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3264.458057] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3264.893351] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3264.893353] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3264.893354] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3264.893356] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3264.893358] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3264.893360] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3264.893362] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3264.893364] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3264.893366] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3264.893368] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3264.893370] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3264.893372] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3264.893374] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3264.893377] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3264.893379] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3264.893381] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3264.893383] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3264.893384] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3264.893386] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3264.893388] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3264.893389] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3264.893391] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3264.893393] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3264.893395] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3264.909968] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3264.909971] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3264.909974] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3264.909976] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3264.909978] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3264.909981] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3264.909983] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3264.909985] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3264.909987] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3264.909989] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3265.226953] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3265.226955] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3265.226957] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3265.226959] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3265.226961] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3265.226963] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3265.226965] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3265.226967] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3265.226969] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3265.226971] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3265.226972] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3265.226974] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3265.226980] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3265.226982] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3265.226984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3265.226986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3265.226988] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3265.226990] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3265.226992] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3265.226994] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3265.226996] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3265.226998] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3265.226999] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3265.227001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3265.227003] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3265.227005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3265.227007] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3265.227008] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3265.227010] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3265.227012] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3265.227014] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3265.227016] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3265.227018] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3265.227020] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3265.227022] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3265.227024] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3265.227026] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3265.227028] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3265.427111] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3265.427113] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3265.427115] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3265.427118] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3265.427120] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3265.427122] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3265.427124] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3265.427126] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3265.427128] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3265.427130] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3265.427132] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3265.427134] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3265.427136] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3265.427138] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3265.427140] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3265.427142] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3265.427144] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3265.427146] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3265.427151] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3265.427153] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3265.427155] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3265.427157] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3265.427159] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3265.427161] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3265.427163] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3265.427165] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3265.427167] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3265.427169] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3265.427171] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3265.427173] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3265.427174] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3265.427176] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3265.427178] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3265.427180] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3265.427182] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3265.427184] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3265.427186] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3265.427188] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3265.933981] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3265.933982] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3265.933984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3265.933986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3265.934012] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3265.934038] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3265.934054] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3265.934078] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3265.934102] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3265.934114] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3265.934116] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3265.934118] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3265.934120] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3265.934122] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3265.934124] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3265.934126] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3265.934128] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3265.934130] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3265.934132] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3265.934134] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3265.934136] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3265.934139] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3265.934141] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3265.934143] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3265.934144] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3265.934146] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3265.934148] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3265.934149] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3265.934151] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3265.934153] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3265.934155] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3265.934156] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3266.291210] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3266.291212] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3266.291215] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3266.291217] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3266.291219] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3266.291221] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3266.291224] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3266.291226] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3266.291228] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3266.291230] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3266.291232] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3266.291234] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3266.291236] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3266.291238] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3266.291240] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3266.291242] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3266.291244] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3266.291246] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3266.291248] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3266.291250] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3266.291252] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3266.291254] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3266.291256] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3266.291261] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3266.291263] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3266.291265] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3266.291267] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3266.291269] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3266.291271] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3266.291273] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3266.291275] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3266.291277] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3266.291279] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3266.291281] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3266.291283] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3266.291285] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3266.291287] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3266.594965] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3266.594968] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3266.594969] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3266.594971] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3266.594973] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3266.594976] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3266.594978] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3266.594980] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3266.594982] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3266.594984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3266.594986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3266.594988] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3266.594990] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3266.594993] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3266.594995] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3266.594997] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3266.594999] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3266.595001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3266.595006] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3266.595008] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3266.595010] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3266.595012] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3266.595014] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3266.595016] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3266.595018] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3266.595020] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3266.595022] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3266.595024] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3266.595026] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3266.595028] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3266.595030] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3266.595032] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3266.595033] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3266.595035] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3266.595037] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3266.595039] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3266.595041] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3266.595043] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3266.945267] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3266.945304] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3266.945306] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3266.945309] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3266.945311] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3266.945313] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3266.945315] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3266.945317] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3266.945319] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3266.945322] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3266.945323] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3266.945326] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3266.945328] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3266.945330] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3266.945332] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3266.945334] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3266.945336] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3266.945338] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3266.945340] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3266.945342] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3266.945344] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3266.945346] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3266.945348] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3266.945350] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3266.945352] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3266.945354] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3266.945356] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3266.945358] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3266.945362] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3266.945365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3266.945366] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3266.945368] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3266.945370] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3266.945372] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3266.945374] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3266.945376] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3267.262405] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3267.278875] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3267.278940] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3267.278943] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3267.278946] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3267.278948] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3267.278950] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3267.278952] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3267.278955] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3267.278957] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3267.278959] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3267.278961] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3267.278963] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3267.278965] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3267.278967] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3267.278969] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3267.278971] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3267.278973] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3267.278975] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3267.278978] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3267.278980] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3267.278982] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3267.278984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3267.278986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3267.278988] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3267.278990] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3267.278992] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3267.278994] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3267.278996] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3267.278998] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3267.279003] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3267.279006] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3267.279008] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3267.279010] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3267.279012] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3267.596413] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3267.596415] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3267.596417] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3267.596419] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3267.596423] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3267.596424] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3267.596426] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3267.596428] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3267.596430] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3267.596431] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3267.596433] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3267.596435] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3267.596437] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3267.596439] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3267.596440] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3267.612539] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3267.612599] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3267.612622] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3267.612624] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3267.612626] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3267.612629] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3267.612631] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3267.612633] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3267.612635] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3267.612637] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3267.612639] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3267.612641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3267.612643] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3267.612646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3267.612648] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3267.612650] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3267.612652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3267.612654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3267.612656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3267.612658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3267.612660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3267.612662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3267.929637] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3267.929639] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3267.929641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3267.929643] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3267.929645] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3267.929647] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3267.929649] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3267.929651] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3267.929653] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3267.929655] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3267.929657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3267.929659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3267.929661] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3267.929663] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3267.929665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3267.929667] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3267.929669] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3267.929671] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3267.929673] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3267.929675] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3267.929677] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3267.929682] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3267.929684] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3267.929686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3267.929688] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3267.929690] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3267.929692] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3267.929694] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3267.929695] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3267.929697] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3267.929699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3267.929701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3267.929703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3267.929704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3267.929706] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3267.929708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3267.929710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3267.929712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3268.263364] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3268.263366] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3268.263368] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3268.263370] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3268.263371] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3268.263373] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3268.263375] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3268.263377] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3268.263379] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3268.263381] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3268.263383] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3268.263385] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3268.263387] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3268.263389] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3268.263391] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3268.263395] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3268.263396] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3268.263398] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3268.263400] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3268.263401] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3268.263403] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3268.263405] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3268.263407] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3268.263408] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3268.263410] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3268.263412] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3268.279891] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3268.279943] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3268.279945] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3268.279948] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3268.279950] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3268.279953] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3268.279955] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3268.279957] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3268.279959] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3268.279961] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3268.279963] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3268.279965] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3268.613535] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3268.613579] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3268.613603] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3268.613605] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3268.613608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3268.613610] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3268.613612] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3268.613614] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3268.613616] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3268.613619] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3268.613621] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3268.613623] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3268.613625] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3268.613627] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3268.613630] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3268.613632] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3268.613634] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3268.613636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3268.613638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3268.613640] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3268.613642] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3268.613644] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3268.613646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3268.613649] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3268.613650] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3268.613652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3268.613654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3268.613656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3268.613658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3268.613663] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3268.613666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3268.613668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3268.613670] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3268.613672] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3268.931418] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3268.931420] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3268.931422] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3268.931424] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3268.931426] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3268.931428] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3268.931430] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3268.931432] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3268.931434] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3268.931436] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3268.931439] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3268.931440] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3268.931442] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3268.931444] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3268.931446] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3268.931447] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3268.931449] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3268.931451] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3268.931453] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3268.931454] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3268.931456] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3268.947283] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3268.947331] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3268.947334] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3268.947336] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3268.947339] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3268.947341] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3268.947343] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3268.947345] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3268.947347] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3268.947350] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3268.947352] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3268.947354] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3268.947356] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3268.947358] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3268.947361] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3268.947363] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3269.264361] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3269.264363] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3269.264365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3269.264367] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3269.264369] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3269.264371] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3269.264373] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3269.264374] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3269.264378] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3269.264379] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3269.264381] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3269.264383] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3269.264385] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3269.264386] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3269.264388] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3269.264390] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3269.264392] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3269.264393] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3269.264395] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3269.285296] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3269.285316] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3269.285332] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3269.285334] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3269.285349] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3269.285352] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3269.285354] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3269.285370] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3269.285372] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3269.285387] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3269.285390] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3269.285413] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3269.285416] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3269.285418] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3269.285421] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3269.598036] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3269.598037] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3269.598039] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3269.598041] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3269.598043] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3269.598045] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3269.598047] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3269.598049] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3269.598051] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3269.598053] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3269.598055] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3269.598057] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3269.598061] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3269.598062] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3269.598064] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3269.598066] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3269.598072] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3269.598074] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3269.598076] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3269.598077] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3269.598079] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3269.598081] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3269.598083] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3269.614523] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3269.614580] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3269.614583] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3269.614605] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3269.614607] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3269.614610] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3269.614612] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3269.614614] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3269.614626] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3269.614628] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3269.614631] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3269.614633] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3269.614636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3269.614638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3269.915038] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3269.915040] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3269.915043] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3269.915045] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3269.915047] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3269.915048] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3269.915050] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3269.915052] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3269.915054] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3269.915055] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3269.915057] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3269.915059] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3269.915060] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3269.931526] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3269.931589] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3269.931592] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3269.931594] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3269.931596] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3269.931599] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3269.931601] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3269.931603] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3269.931606] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3269.931608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3269.931610] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3269.931612] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3269.931614] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3269.931616] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3269.931618] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3269.931620] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3269.931622] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3269.931624] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3269.931626] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3269.931629] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3269.931631] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3269.931633] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3269.931635] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3269.931637] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3270.265359] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3270.265361] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3270.265363] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3270.265365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3270.265367] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3270.265369] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3270.265371] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3270.265373] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3270.265375] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3270.265377] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3270.265378] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3270.265380] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3270.265382] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3270.265384] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3270.265386] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3270.265388] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3270.265390] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3270.265392] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3270.265394] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3270.265395] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3270.265397] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3270.265399] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3270.265401] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3270.265403] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3270.265405] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3270.265409] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3270.265410] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3270.265412] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3270.265414] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3270.265416] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3270.265417] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3270.265419] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3270.265421] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3270.265422] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3270.265424] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3270.265426] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3270.281878] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3270.281941] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3270.281944] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3270.615622] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3270.615624] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3270.615626] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3270.615628] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3270.615630] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3270.615632] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3270.615634] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3270.615636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3270.615638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3270.615640] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3270.615642] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3270.615644] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3270.615646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3270.615648] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3270.615650] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3270.615652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3270.615657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3270.615660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3270.615662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3270.615664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3270.615666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3270.615668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3270.615670] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3270.615672] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3270.615673] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3270.615675] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3270.615677] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3270.615679] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3270.615681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3270.615682] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3270.615684] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3270.615686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3270.615688] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3270.615690] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3270.615692] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3270.615694] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3270.615696] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3270.615698] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3270.849659] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3270.866144] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3270.866159] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3270.866162] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3270.866184] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3270.866187] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3270.866189] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3270.866191] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3270.866194] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3270.866196] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3270.866198] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3270.866200] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3270.866205] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3270.866207] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3270.866209] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3270.866211] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3270.866213] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3270.866215] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3270.866242] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3270.866244] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3270.866246] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3270.866249] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3270.866251] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3270.866253] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3270.866295] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3270.866298] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3270.866300] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3270.866302] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3270.866305] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3270.866307] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3270.866351] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3270.866353] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3270.866355] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3270.866357] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3271.260454] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3271.260456] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3271.260458] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3271.260460] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3271.260462] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3271.260464] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3271.260466] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3271.260467] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3271.260469] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3271.260471] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3271.260473] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3271.260474] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3271.260476] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3271.260478] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3271.260480] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3271.260482] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3271.260484] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3271.260486] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3271.260488] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3271.260490] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3271.260492] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3271.260494] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3271.260496] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 11906
[ 3271.260499] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3271.260501] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3271.260503] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3271.260504] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3271.260506] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3271.260508] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3271.260509] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3271.260511] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3271.260513] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3271.260515] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3271.260516] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3271.496837] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3271.496838] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3271.496840] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3271.496842] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3271.496844] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3271.496845] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3271.496847] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3271.496849] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3271.496851] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3271.496852] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3271.496854] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3271.502256] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3271.502268] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3271.502270] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3271.502272] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3271.502275] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3271.502277] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3271.502279] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3271.502281] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3271.502283] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3271.502285] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3271.502288] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3271.502289] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3271.502292] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3271.502294] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3271.502296] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3271.502298] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3271.502300] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3271.502302] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3271.502304] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3271.502306] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3271.502308] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3271.502310] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3271.502312] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3271.502314] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3271.502315] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3271.696987] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3271.696988] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3271.696990] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3271.696992] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3271.696993] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3271.696995] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3271.696997] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3271.696999] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3271.697000] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3271.697002] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3271.729986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3271.730041] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3271.730044] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3271.730061] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3271.730063] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3271.730086] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3271.730089] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3271.730091] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3271.730093] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3271.730096] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3271.730098] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3271.730100] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3271.730103] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3271.730105] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3271.730107] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3271.730109] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3271.730111] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3271.730116] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3271.730119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3271.730121] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3272.022258] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3272.022260] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3272.022262] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 20988
[ 3272.022266] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3272.022267] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3272.022269] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3272.022271] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3272.022272] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3272.022274] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3272.022276] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3272.022278] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3272.022279] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3272.022281] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3272.022283] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3272.054627] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3272.054679] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3272.054682] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3272.054684] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3272.054701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3272.054703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3272.054719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3272.054721] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3272.054742] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3272.054745] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3272.054747] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3272.054753] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3272.054756] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3272.054758] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3272.054760] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3272.054762] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3272.347751] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3272.347754] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3272.347756] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3272.347758] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3272.347763] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3272.347765] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3272.347768] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3272.347770] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3272.347772] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3272.347774] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3272.347776] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3272.347778] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3272.347780] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3272.347782] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3272.347784] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3272.347786] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3272.347788] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3272.347790] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3272.347792] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3272.347793] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3272.347795] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3272.347797] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3272.347799] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3272.347801] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3272.347802] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3272.347804] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3272.347806] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3272.347808] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3272.347810] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3272.347812] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3272.347814] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3272.347816] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3272.347818] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3272.347820] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3272.347822] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3272.347824] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 22860
[ 3272.347827] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3272.347829] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3272.347831] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3272.770641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3272.770643] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3272.770666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3272.770679] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3272.770682] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3272.770685] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3272.770687] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3272.770689] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3272.770691] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3272.770697] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3272.770700] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3272.770703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3272.770705] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3272.770707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3272.770709] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3272.770711] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3272.770713] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3272.770715] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3272.770717] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3272.770719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3272.770721] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3272.770724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3272.770726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3272.770728] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3272.770730] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3272.770732] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3272.770734] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3272.770736] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3272.770738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3272.770740] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3272.770742] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3272.770744] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3272.770745] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3272.770747] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3272.770749] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3272.770751] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3272.770753] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3273.268447] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3273.268514] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3273.268517] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3273.268520] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3273.268522] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3273.268524] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3273.268527] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3273.268529] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3273.268532] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3273.268534] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3273.268536] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3273.268539] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3273.268541] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3273.268543] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3273.268545] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3273.268547] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3273.268549] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3273.268551] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3273.268553] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3273.268555] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3273.268557] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3273.268559] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3273.268561] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3273.268563] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3273.268565] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3273.268567] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3273.268569] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3273.268571] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3273.268573] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3273.268575] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3273.268577] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3273.268579] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3273.268580] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3273.268582] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3273.618929] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3273.618931] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3273.618933] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3273.618935] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3273.618937] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3273.618939] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3273.618941] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3273.618943] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3273.618945] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3273.618947] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3273.618949] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3273.618951] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3273.618953] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3273.618955] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3273.618957] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3273.618959] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3273.618961] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3273.618963] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3273.618965] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3273.618967] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3273.618969] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3273.618971] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3273.618982] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3273.618984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3273.618986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3273.618988] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3273.618990] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3273.618992] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3273.618994] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3273.618996] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3273.618998] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3273.619000] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3273.619002] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3273.619003] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3273.619005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3273.619007] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3273.619009] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3273.619011] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3273.934733] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3273.934734] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3273.934736] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3273.934738] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3273.934740] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3273.934741] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3273.934743] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3273.934745] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3273.934746] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3273.934748] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3273.934750] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3273.934757] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3273.934759] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3273.934761] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3273.934763] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3273.934765] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3273.934767] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3273.934769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3273.934771] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3273.934773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3273.934775] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3273.934785] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3273.934787] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3273.934789] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3273.934791] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3273.934793] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3273.934795] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3273.934797] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3273.934799] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3273.934802] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3273.934804] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3273.934806] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3273.934808] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3273.934810] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3273.934812] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3273.934814] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3273.934816] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3274.301950] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3274.301953] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3274.301970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3274.301972] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3274.301987] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3274.301990] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3274.301992] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3274.302010] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3274.302012] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3274.302028] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3274.302030] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3274.302058] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3274.302061] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3274.302063] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3274.302065] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3274.302067] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3274.302069] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3274.302071] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3274.302073] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3274.302075] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3274.302079] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3274.302081] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3274.302084] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3274.302086] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3274.302088] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3274.302090] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3274.302092] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3274.302095] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3274.302097] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3274.302099] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3274.302101] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3274.302103] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3274.302105] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 30565
[ 3274.302109] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3274.302110] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3274.302112] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3274.302114] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3274.302116] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3274.302117] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3274.302119] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3274.569875] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3274.569877] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3274.569879] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3274.569881] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3274.569883] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3274.569885] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3274.569887] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3274.569889] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3274.569891] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3274.569893] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3274.569895] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3274.569897] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3274.569899] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3274.569901] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3274.569903] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 31068
[ 3274.569906] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3274.569908] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3274.569910] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3274.569912] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3274.569913] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3274.569915] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3274.569917] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3274.569919] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3274.569920] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3274.569922] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3274.569924] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3274.569931] mtk_tty_vuart: DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3274.569933] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 31068
[ 3274.569936] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3274.569938] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3274.569940] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3274.569941] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3274.569943] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3274.569945] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3274.569947] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3274.569948] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3274.569950] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3274.569952] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3274.569953] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3274.936976] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3274.936978] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 31564
[ 3274.936981] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3274.936983] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3274.936984] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3274.936986] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3274.936988] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3274.936990] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3274.936991] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3274.936993] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3274.936995] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3274.936997] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3274.936998] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3274.937007] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3274.937009] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 31564
[ 3274.937012] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3274.937014] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3274.937015] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3274.937017] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3274.937019] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3274.937021] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3274.937022] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3274.937024] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3274.937026] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3274.937028] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3274.937029] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3274.953651] mtk_tty_vuart: OR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3274.953707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3274.953737] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3274.953739] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3274.953742] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3274.953744] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3274.953746] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3274.953748] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3274.953751] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3274.953753] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3274.953755] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3275.287266] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3275.287268] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3275.287270] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3275.287271] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3275.287273] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3275.287275] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3275.287277] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3275.287279] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3275.287281] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3275.287283] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3275.287284] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3275.287286] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3275.287288] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3275.287290] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3275.287292] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3275.287294] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3275.287295] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3275.287298] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3275.287299] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3275.287301] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3275.287303] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3275.287308] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3275.287310] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3275.287312] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3275.287314] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3275.287316] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 31898
[ 3275.287320] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3275.287321] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3275.287323] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3275.287325] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3275.287327] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3275.287328] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3275.287330] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3275.287332] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3275.287334] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3275.287335] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3275.287337] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3275.287344] mtk_tty_vuart: ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3275.287347] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3275.637487] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3275.637504] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3275.637522] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3275.637524] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3275.637545] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3275.637548] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3275.637551] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3275.637553] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3275.637555] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3275.637558] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3275.637560] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3275.637562] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3275.637564] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3275.637566] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3275.637568] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3275.637570] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3275.637572] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3275.637574] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3275.637576] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3275.637578] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3275.637580] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3275.637582] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3275.637584] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3275.637586] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3275.637590] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3275.637593] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3275.637594] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3275.637596] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3275.637598] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3275.637600] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3275.637602] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3275.637604] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3275.637605] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3275.637608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3275.637610] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3275.637612] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3275.637614] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3275.954642] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3275.954644] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3275.954646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32312
[ 3275.954649] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3275.954651] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3275.954653] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3275.954655] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3275.954656] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3275.954658] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3275.954660] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3275.954661] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3275.954663] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3275.954665] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3275.954667] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3275.971563] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3275.971565] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3275.971588] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3275.971590] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3275.971593] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3275.971595] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3275.971597] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3275.971599] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3275.971601] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3275.971604] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3275.971606] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3275.971608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3275.971610] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3275.971612] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3275.971614] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3275.971616] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3275.971618] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3275.971624] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3275.971626] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3276.304873] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3276.304875] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3276.304877] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3276.304879] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3276.304881] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3276.304886] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3276.304888] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3276.304890] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3276.304892] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3276.304894] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3276.304896] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3276.304898] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3276.304899] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3276.304901] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3276.304903] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3276.304905] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3276.304907] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3276.304909] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3276.304911] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3276.304912] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3276.304914] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3276.304916] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3276.304918] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3276.304943] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3276.304946] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3276.304948] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3276.304950] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3276.304952] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3276.304954] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3276.304956] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32443
[ 3276.304959] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3276.304961] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3276.304963] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3276.304964] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3276.304966] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3276.304968] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3276.304970] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3276.304971] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3276.304973] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3276.304975] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3276.605893] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3276.605898] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3276.605900] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3276.605902] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3276.605904] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3276.605905] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3276.605907] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3276.605909] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3276.605911] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3276.605913] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3276.605915] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3276.605917] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3276.605919] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3276.605921] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3276.605923] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3276.605925] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3276.605927] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3276.605929] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32522
[ 3276.605932] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3276.605934] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3276.605936] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3276.605938] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3276.605939] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3276.605941] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3276.605943] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3276.605945] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3276.605946] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3276.605948] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3276.605950] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3276.605958] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3276.605961] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3276.605963] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3276.605965] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32522
[ 3276.605968] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3276.622221] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3276.622240] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3276.622257] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3276.972300] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3276.972301] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3276.972303] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3276.972305] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3276.972307] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3276.972308] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3276.972310] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3276.972312] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3276.972314] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3276.972323] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3276.972325] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3276.972327] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3276.972329] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3276.972331] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32593
[ 3276.972335] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3276.972336] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3276.972338] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3276.972340] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3276.972342] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3276.972344] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3276.972345] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3276.972347] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3276.972349] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3276.972351] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3276.972352] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3276.988842] mtk_tty_vuart: SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3276.988886] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3276.988903] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3276.988907] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3276.988946] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3276.988949] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3276.988952] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3276.988954] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3276.988956] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3276.988958] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3276.988961] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3277.305905] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3277.305907] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3277.305911] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3277.305914] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3277.305916] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3277.305917] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3277.305920] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3277.305922] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3277.305923] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3277.305925] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3277.305927] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3277.305929] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3277.305931] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3277.305933] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3277.305934] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3277.305937] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3277.305939] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3277.305941] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3277.305942] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3277.305944] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3277.305946] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3277.305948] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3277.305950] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3277.305952] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3277.305954] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32637
[ 3277.305958] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3277.305959] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3277.305961] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3277.305963] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3277.305964] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3277.305966] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3277.305968] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3277.305970] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3277.305971] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3277.305973] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3277.305975] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3277.305984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3277.305986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3277.640559] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3277.640561] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3277.640563] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3277.640565] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32673
[ 3277.640568] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3277.640570] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3277.640571] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3277.640573] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3277.640575] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3277.640577] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3277.640579] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3277.640580] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3277.640582] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3277.640584] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3277.640585] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3277.640594] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3277.640596] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3277.640598] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3277.640600] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3277.640602] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3277.640604] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32673
[ 3277.640607] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3277.640609] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3277.640610] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3277.640612] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3277.640614] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3277.640616] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3277.640617] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3277.640619] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3277.640621] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3277.640623] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3277.640624] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3277.656198] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3277.656215] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3277.656217] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3277.656235] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3277.973372] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3277.973374] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3277.973375] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3278.007750] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3278.007754] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3278.007770] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3278.007772] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3278.007789] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3278.007791] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3278.007807] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3278.007810] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3278.007812] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3278.007834] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3278.007837] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3278.007842] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3278.007845] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3278.007847] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3278.007849] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3278.007851] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3278.007854] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3278.007856] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3278.007858] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3278.007860] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3278.007862] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3278.007864] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3278.007866] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3278.007868] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3278.007870] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3278.007871] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3278.337012] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3278.337014] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3278.337016] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3278.337018] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3278.337021] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3278.337023] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3278.337025] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3278.337027] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3278.337029] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3278.337047] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3278.337049] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3278.337051] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3278.337053] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3278.337055] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3278.337057] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3278.337062] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3278.337064] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3278.337066] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3278.337068] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3278.337070] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3278.337072] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3278.337074] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3278.337076] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3278.337078] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3278.337080] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3278.337083] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3278.337085] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3278.337087] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3278.337089] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32713
[ 3278.337121] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3278.337133] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3278.337135] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3278.337137] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3278.337139] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3278.337141] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3278.337143] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3278.337145] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3278.337146] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3278.337148] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3278.337150] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3278.623567] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3278.623569] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3278.623572] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3278.623574] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3278.623579] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3278.623581] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3278.623584] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3278.623603] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3278.623605] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3278.623607] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3278.623611] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3278.623614] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3278.623616] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3278.623618] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3278.623620] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3278.623623] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3278.623624] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3278.623662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3278.623664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3278.623666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3278.623668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3278.623670] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3278.623672] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3278.623674] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3278.623717] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3278.623719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3278.623721] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3278.623723] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3278.623726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3278.623728] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3278.623730] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3278.623773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3278.623775] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3278.623778] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3278.623780] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3278.623782] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3278.623785] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3278.623828] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3278.973980] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3278.973982] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3278.974022] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3278.974024] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3278.974026] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3278.974028] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3278.974030] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3278.974032] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3278.974035] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3278.974078] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3278.974080] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3278.974082] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3278.974085] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3278.974087] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3278.974089] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3278.974137] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3278.974139] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3278.974141] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3278.974143] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3278.974144] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3278.974146] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3278.974148] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3278.974150] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3278.974152] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3278.974191] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3278.974194] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3278.974201] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3278.974203] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3278.974205] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3278.974248] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3278.974251] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3278.974253] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3278.974255] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3278.974257] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3278.974260] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3278.974262] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3278.974315] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3278.974317] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3278.974319] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3279.290931] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3279.290933] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3279.290935] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3279.290937] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3279.290939] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3279.290941] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3279.290965] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3279.290967] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3279.290969] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3279.290971] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3279.290973] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3279.290975] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3279.290976] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3279.290978] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3279.291023] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3279.291025] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3279.291027] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3279.291030] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3279.291032] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3279.291034] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3279.291077] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3279.291080] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3279.291082] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3279.291084] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3279.291087] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3279.291089] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3279.291134] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3279.291137] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3279.291139] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3279.291141] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3279.291143] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3279.291144] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3279.291146] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3279.291148] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3279.291150] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3279.291152] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3279.291206] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3279.291215] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3279.291217] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3279.641598] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3279.641601] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3279.641613] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3279.641616] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3279.641618] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3279.641646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3279.641649] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3279.641651] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3279.641654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3279.641656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3279.641658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3279.641660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3279.641663] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3279.641664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3279.641666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3279.641668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3279.641670] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3279.641704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3279.641706] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3279.641708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3279.641710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3279.641712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3279.641714] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3279.641716] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3279.641717] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3279.641757] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3279.641760] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3279.641762] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3279.641764] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3279.641766] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3279.641768] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3279.641812] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3279.641815] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3279.641817] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3279.641819] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3279.641822] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3279.974946] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3279.974949] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3279.974952] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3279.974972] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3279.974975] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3279.974977] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3279.974979] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3279.974981] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3279.974984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3279.974986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3279.974988] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3279.974993] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3279.974995] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3279.974997] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3279.975000] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3279.975002] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3279.975004] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3279.975006] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3279.975025] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3279.975028] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3279.975030] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3279.975031] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3279.975034] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3279.975036] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3279.975038] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3279.975040] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3279.975084] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3279.975087] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3279.975089] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3279.975092] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3279.975094] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3279.975096] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3279.975141] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3279.975143] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3279.975146] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3279.975149] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3279.975151] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3280.275697] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3280.275698] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3280.275700] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3280.275702] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3280.291924] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3280.291962] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3280.291965] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3280.291968] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3280.291970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3280.291972] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3280.291974] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3280.291976] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3280.291978] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3280.291981] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3280.291983] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3280.291985] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3280.291987] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3280.291989] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3280.291991] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3280.291993] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3280.291995] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3280.291997] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3280.291999] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3280.292001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3280.292002] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3280.292004] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3280.292006] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3280.292008] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3280.292010] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3280.292012] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3280.292016] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3280.292019] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3280.292021] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3280.292023] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3280.292025] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3280.292027] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3280.292049] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3280.642303] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3280.642306] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3280.642309] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3280.642329] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3280.642332] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3280.642334] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3280.642336] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3280.642339] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3280.642341] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3280.642343] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3280.642345] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3280.642347] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3280.642349] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3280.642351] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3280.642353] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3280.642355] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3280.642357] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3280.642359] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3280.642361] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3280.642362] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3280.642364] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3280.642366] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3280.642368] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3280.642370] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3280.642372] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3280.642373] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3280.642378] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3280.642381] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3280.642383] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3280.642385] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3280.642387] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3280.642389] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3280.642391] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3280.642393] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3280.642395] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3280.642397] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3280.959257] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3280.959260] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3280.959263] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3280.959265] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3280.959267] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3280.959271] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3280.959275] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3280.959277] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3280.959279] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3280.959296] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3280.959299] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3280.959303] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3280.959305] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3280.959307] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3280.959309] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3280.959311] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3280.959313] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3280.959315] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3280.959357] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3280.959359] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3280.959361] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3280.959363] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3280.959365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3280.959368] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3280.959370] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3280.959372] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3280.959411] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3280.959414] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3280.959416] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3280.959418] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3280.959420] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3280.959423] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3280.959467] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3280.959470] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3280.959472] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3280.959474] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3281.310564] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3281.310587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3281.310590] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3281.310593] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3281.310595] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3281.310597] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3281.310599] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3281.310601] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3281.310603] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3281.310606] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3281.310608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3281.310612] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3281.310615] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3281.310617] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3281.310619] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3281.310621] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3281.310639] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3281.310641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3281.310645] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3281.310647] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3281.310649] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3281.310651] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3281.310653] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3281.310655] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3281.310657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3281.310699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3281.310701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3281.310703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3281.310705] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3281.310708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3281.310710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3281.610106] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3281.610107] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3281.639845] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3281.639849] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3281.639851] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3281.639854] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3281.639856] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3281.639860] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3281.639863] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3281.639865] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3281.639868] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3281.639870] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3281.639887] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3281.639891] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3281.639893] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3281.639895] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3281.639897] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3281.639900] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3281.639902] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3281.639904] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3281.639947] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3281.639950] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3281.639951] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3281.639953] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3281.639955] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3281.639957] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3281.639959] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3281.639961] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3281.640002] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3281.640005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3281.640007] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3281.640009] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3281.640012] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3281.640014] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3281.640078] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3281.640081] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3281.960417] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3281.960419] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3281.960420] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3281.976922] mtk_tty_vuart: YS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3281.976939] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3281.976942] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3281.976944] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3281.976946] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3281.976949] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3281.976952] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3281.976954] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3281.976974] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3281.976976] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3281.976979] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3281.976981] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3281.976984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3281.976987] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3281.976989] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3281.976991] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3281.976993] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3281.976995] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3281.976997] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3281.977032] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3281.977035] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3281.977037] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3281.977039] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3281.977041] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3281.977043] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3281.977045] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3281.977087] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3281.977089] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3281.977091] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3281.977094] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3281.977096] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3281.977098] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3281.977143] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3281.977145] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3282.293914] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3282.293917] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3282.293919] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3282.293922] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3282.293926] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3282.293929] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3282.293931] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3282.293934] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3282.293936] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3282.293938] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3282.293957] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3282.293960] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3282.293962] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3282.293964] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3282.293966] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3282.293968] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3282.293970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3282.293973] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3282.294015] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3282.294017] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3282.294019] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3282.294021] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3282.294023] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3282.294025] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3282.294070] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3282.294072] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3282.294075] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3282.294077] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3282.294079] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3282.294081] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3282.294127] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3282.294129] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3282.294132] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3282.294135] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3282.294137] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3282.294139] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3282.294141] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3282.294143] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3282.294145] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3282.644245] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3282.644247] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3282.644250] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3282.644252] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3282.644257] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3282.644260] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3282.644262] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3282.644264] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3282.644267] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3282.644269] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3282.644271] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3282.644295] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3282.644298] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3282.644300] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3282.644302] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3282.644304] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3282.644306] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3282.644308] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3282.644350] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3282.644352] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3282.644354] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3282.644356] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3282.644359] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3282.644361] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3282.644363] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3282.644406] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3282.644408] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3282.644410] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3282.644413] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3282.644415] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3282.644417] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3282.644461] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3282.644464] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3282.644467] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3282.644469] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3282.644471] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3282.644473] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3282.644475] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3282.644477] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3282.644478] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3282.644519] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3282.644522] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3282.644523] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3282.644525] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3282.644533] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3282.644535] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3282.644576] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3282.644578] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3282.644580] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3282.644583] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3282.644585] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3282.644587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3282.644631] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3282.644634] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3282.644637] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3282.644639] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3282.644641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3282.644645] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3282.644647] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3282.644700] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3282.644702] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3282.644704] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3282.644706] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3282.644708] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3282.644710] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3282.644712] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3282.644713] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3282.644715] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3282.660944] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3282.660968] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3282.660970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3282.660973] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3282.660975] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3282.660977] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3282.660980] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3282.660982] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3282.660985] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3282.961257] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3282.961259] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3282.961261] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3282.961280] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3282.961296] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3282.961298] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3282.961301] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3282.961303] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3282.961305] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3282.961307] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3282.961310] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3282.961312] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3282.961314] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3282.961316] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3282.961318] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3282.961338] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3282.961340] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3282.961342] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3282.961346] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3282.961348] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3282.961350] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3282.961352] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3282.961354] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3282.961356] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3282.961395] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3282.961398] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3282.961400] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3282.961402] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3282.961404] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3282.961406] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3282.961450] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3282.961452] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3282.961456] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3282.961458] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3282.961460] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3282.961462] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3282.961463] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3282.961465] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3282.961467] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3282.961508] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3283.312053] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3283.312055] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3283.312057] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3283.312059] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3283.312078] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3283.312080] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3283.312082] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3283.312084] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3283.312086] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3283.312088] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3283.312089] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3283.312092] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3283.312101] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3283.312103] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3283.312105] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3283.312107] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3283.312135] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3283.312137] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3283.312140] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3283.312148] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3283.312151] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3283.312153] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3283.312155] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3283.312157] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3283.312160] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3283.312198] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3283.312203] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3283.312204] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3283.312206] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3283.312209] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3283.312210] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3283.312212] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3283.312214] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3283.312216] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3283.312254] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3283.312256] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3283.312258] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3283.312266] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3283.312268] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3283.628657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3283.628660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3283.628662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3283.628665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3283.628667] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3283.628670] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3283.628672] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3283.628674] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3283.628676] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3283.628678] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3283.628688] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3283.628707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3283.628709] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3283.628711] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3283.628713] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3283.628715] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3283.628717] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3283.628719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3283.628728] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3283.628730] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3283.628732] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3283.628735] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3283.628737] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3283.628739] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3283.628769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3283.628771] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3283.628773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3283.628776] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3283.628779] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3283.628781] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3283.628824] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3283.628828] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3283.628830] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3283.628832] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3283.628834] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3283.628836] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3283.628838] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3283.628840] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3283.628842] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3283.628843] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3283.962658] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3283.978955] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3283.978958] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3283.978960] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3283.978963] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3283.978965] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3283.978969] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3283.978971] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3283.978973] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3283.978991] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3283.978994] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3283.978996] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3283.978998] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3283.979001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3283.979004] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3283.979006] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3283.979008] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3283.979010] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3283.979012] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3283.979014] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3283.979016] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3283.979053] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3283.979055] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3283.979058] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3283.979060] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3283.979062] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3283.979064] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3283.979120] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3283.979122] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3283.979124] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3283.979126] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3283.979129] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3283.979131] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3283.979166] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3283.979168] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3283.979170] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3283.979172] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3284.279381] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3284.279383] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3284.279384] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3284.279386] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3284.279388] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3284.295876] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3284.295910] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3284.295913] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3284.295915] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3284.295918] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3284.295923] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3284.295925] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3284.295927] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3284.295930] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3284.295932] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3284.295950] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3284.295953] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3284.295961] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3284.295963] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3284.295965] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3284.295967] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3284.295970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3284.296008] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3284.296011] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3284.296014] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3284.296016] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3284.296018] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3284.296020] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3284.296067] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3284.296069] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3284.296072] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3284.296074] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3284.296076] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3284.296078] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3284.296123] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3284.296126] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3284.629728] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3284.629730] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3284.629732] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3284.629734] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3284.629736] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3284.629738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3284.629741] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3284.629743] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3284.629746] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3284.629748] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3284.629750] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3284.629752] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3284.629753] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3284.629755] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3284.629757] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3284.629759] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3284.629760] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3284.629762] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3284.629764] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3284.646260] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3284.646273] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3284.646275] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3284.646277] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3284.646280] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3284.646284] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3284.646286] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3284.646288] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3284.646291] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3284.646293] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3284.646311] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3284.646315] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3284.646317] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3284.646319] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3284.646321] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3284.646323] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3284.646325] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3284.646370] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3284.960592] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3284.960594] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3284.960596] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3284.960597] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3284.960600] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3284.960601] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3284.980861] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3284.980902] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3284.980918] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3284.980921] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3284.980923] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3284.980944] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3284.980947] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3284.980949] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3284.980951] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3284.980954] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3284.980956] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3284.980958] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3284.980960] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3284.980962] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3284.980964] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3284.980966] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3284.980968] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3284.980970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3284.980972] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3284.980974] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3284.980976] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3284.980978] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3284.980980] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3284.980984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3285.280564] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3285.280566] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3285.280568] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3285.280571] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3285.280573] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3285.280577] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3285.280578] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3285.280632] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3285.280635] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3285.280636] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3285.280638] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3285.280640] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3285.280642] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3285.280644] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3285.280646] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3285.280648] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3285.310126] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3285.310141] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3285.310163] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3285.310165] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3285.310168] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3285.310173] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3285.310175] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3285.310178] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3285.310180] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3285.310183] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3285.310185] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3285.310221] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3285.310224] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3285.310226] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3285.310229] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3285.310231] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3285.310233] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3285.310278] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3285.310281] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3285.310283] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3285.310285] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3285.614071] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3285.614073] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3285.614075] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3285.614079] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3285.614080] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3285.614082] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3285.614126] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3285.614128] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3285.614130] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3285.614132] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3285.614134] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3285.614136] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3285.614137] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3285.614139] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3285.630528] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3285.630564] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3285.630566] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3285.630569] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3285.630572] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3285.630575] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3285.630578] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3285.630580] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3285.630582] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3285.630584] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3285.630586] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3285.630607] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3285.630611] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3285.630613] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3285.630627] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3285.630629] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3285.630631] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3285.630665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3285.630668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3285.630670] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3285.630673] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3285.630675] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3285.630677] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3285.947515] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3285.947550] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3285.947553] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3285.947556] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3285.947559] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3285.947563] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3285.947566] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3285.947652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3285.947655] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3285.947710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3285.947713] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3285.947715] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3285.947766] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3285.947769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3285.947771] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3285.947823] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3285.947825] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3285.947835] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3285.947878] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3285.947881] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3285.947934] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3285.947937] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3285.947939] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3285.948005] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3285.948007] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3285.948009] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3285.964288] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3285.964303] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3285.964306] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3285.964308] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3285.964310] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3285.964315] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3285.964318] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3285.964336] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3285.964339] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3285.964341] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3285.964343] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3286.281989] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3286.281991] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3286.281992] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3286.281994] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3286.281996] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3286.281998] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3286.281999] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3286.282001] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3286.282003] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3286.297900] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3286.297917] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3286.297919] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3286.297922] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3286.297925] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3286.297952] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3286.297955] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3286.297957] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3286.297960] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3286.298014] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3286.298128] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3286.298189] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3286.298296] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3286.298358] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3286.298361] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3286.298363] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3286.298364] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3286.298366] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3286.298368] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3286.298370] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3286.298372] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3286.298374] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3286.314566] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3286.314579] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3286.314582] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3286.314584] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3286.314587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3286.314614] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3286.314617] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3286.613752] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3286.613754] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3286.613757] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3286.648892] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3286.648934] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3286.648956] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3286.648959] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3286.648961] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3286.648963] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3286.648966] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3286.648968] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3286.648970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3286.648972] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3286.648974] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3286.648976] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3286.648978] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3286.648980] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3286.648982] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3286.648984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3286.648986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3286.648988] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3286.648990] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3286.648992] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3286.648994] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3286.648996] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3286.648998] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3286.649000] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3286.649002] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3286.948964] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3286.948967] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3286.948970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3286.948972] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3286.948974] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3286.948977] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3286.948979] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3286.949033] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3286.949035] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3286.949037] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3286.949039] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3286.949041] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3286.949043] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3286.949045] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3286.949047] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3286.949049] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3286.980114] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3286.980147] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3286.980150] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3286.980153] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3286.980155] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3286.980160] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3286.980162] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3286.980164] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3286.980167] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3286.980169] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3286.980210] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3286.980214] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3286.980217] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3286.980219] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3286.980221] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3286.980223] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3286.980225] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3286.980260] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3286.980262] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3286.980265] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3286.980267] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3287.298937] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3287.298986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3287.298989] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3287.298991] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3287.298994] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3287.298997] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3287.298999] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3287.299001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3287.299003] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3287.299005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3287.299007] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3287.299009] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3287.299011] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3287.299014] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3287.299016] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3287.299018] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3287.299020] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3287.299022] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3287.299024] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3287.299026] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3287.299028] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3287.299030] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3287.299032] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3287.299037] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3287.299040] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3287.299042] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3287.299044] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3287.299046] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3287.299048] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3287.299050] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3287.299052] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3287.299054] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3287.299056] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3287.299057] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3287.299059] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3287.299061] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3287.632563] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3287.632568] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3287.632571] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3287.632573] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3287.632576] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3287.632578] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3287.632596] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3287.632600] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3287.632602] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3287.632605] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3287.632607] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3287.632609] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3287.632612] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3287.632654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3287.632657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3287.632659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3287.632662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3287.632664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3287.632666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3287.632709] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3287.632712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3287.632714] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3287.632716] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3287.632719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3287.632721] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3287.632766] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3287.632769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3287.632771] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3287.632773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3287.632775] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3287.632777] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3287.632779] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3287.632823] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3287.632825] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3287.632827] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3287.632830] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3287.632832] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3287.966405] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3287.966407] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3287.966409] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3287.966412] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3287.966414] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3287.966416] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3287.966417] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3287.966419] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3287.966421] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3287.966423] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3287.966424] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3287.966426] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3287.966428] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3287.966430] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3287.983071] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3287.983105] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3287.983108] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3287.983110] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3287.983113] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3287.983115] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3287.983117] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3287.983119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3287.983121] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3287.983123] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3287.983125] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3287.983130] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3287.983132] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3287.983135] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3287.983137] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3287.983139] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3287.983141] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3287.983163] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3287.983166] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3287.983168] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3287.983170] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3287.983173] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3287.983175] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3288.300464] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3288.300478] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3288.300481] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3288.300483] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3288.300485] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3288.300496] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3288.300515] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3288.300517] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3288.300520] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3288.300522] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3288.300524] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3288.300534] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3288.300536] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3288.300538] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3288.300541] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3288.300543] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3288.300545] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3288.300578] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3288.300581] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3288.300583] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3288.300585] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3288.300587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3288.300590] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3288.300631] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3288.300634] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3288.300636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3288.300639] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3288.300641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3288.300643] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3288.300687] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3288.300690] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3288.300692] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3288.633860] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3288.633863] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3288.633865] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3288.633867] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3288.633869] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3288.633871] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3288.633873] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3288.633875] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3288.633877] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3288.633879] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3288.633882] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3288.633884] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3288.633886] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3288.633888] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3288.633890] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3288.633891] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3288.633893] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3288.633895] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3288.633897] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3288.633899] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3288.633900] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3288.667847] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3288.667881] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3288.667884] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3288.667887] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3288.667889] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3288.667893] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3288.667897] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3288.667899] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3288.667901] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3288.667903] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3288.967414] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3288.999900] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3288.999935] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3288.999939] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3288.999941] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3288.999944] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3288.999948] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3288.999951] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3288.999953] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3288.999955] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3288.999957] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3288.999960] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3288.999993] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3288.999996] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3288.999998] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3289.000000] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3289.000003] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3289.000005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3289.000067] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3289.000070] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3289.000072] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3289.000075] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3289.000077] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3289.000080] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3289.000109] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3289.000112] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3289.000114] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3289.000116] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3289.000119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3289.000121] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3289.000161] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3289.000164] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3289.000166] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3289.000168] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3289.000170] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3289.317697] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3289.317699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3289.317701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3289.317703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3289.317705] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3289.317707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3289.317710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3289.317712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3289.317714] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3289.317716] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3289.317719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3289.317723] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3289.317724] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3289.317726] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3289.317728] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3289.317729] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3289.317731] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3289.317733] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3289.317735] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3289.317736] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3289.317738] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3289.317740] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3289.334249] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3289.334265] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3289.334268] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3289.334271] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3289.334273] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3289.334279] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3289.334282] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3289.334284] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3289.334286] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3289.334305] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3289.334308] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3289.334312] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3289.334314] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3289.334316] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3289.334319] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3289.618353] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3289.618355] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3289.618357] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3289.634683] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3289.634699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3289.634701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3289.634704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3289.634706] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3289.634727] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3289.634729] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3289.634732] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3289.634735] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3289.634737] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3289.634739] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3289.634741] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3289.634744] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3289.634746] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3289.634748] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3289.634750] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3289.634753] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3289.634757] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3289.634760] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3289.634762] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3289.634780] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3289.634782] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3289.634785] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3289.634787] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3289.634789] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3289.634792] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3289.634794] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3289.634796] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3289.634798] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3289.634802] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3289.634804] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3289.634806] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3289.985394] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3289.985396] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3289.985437] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3289.985440] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3289.985442] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3289.985444] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3289.985447] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3289.985450] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3289.985452] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3289.985506] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3289.985508] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3289.985510] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3289.985512] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3289.985514] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3289.985516] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3289.985518] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3289.985520] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3289.985522] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3290.001915] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3290.001951] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3290.001955] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3290.001957] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3290.001959] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3290.001964] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3290.001967] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3290.001969] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3290.001972] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3290.001974] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3290.001976] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3290.002003] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3290.002006] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3290.002008] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3290.002011] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3290.002013] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3290.002015] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3290.002060] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3290.318588] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3290.318591] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3290.318593] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3290.318598] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3290.318601] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3290.318603] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3290.318606] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3290.318608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3290.318610] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3290.318638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3290.318640] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3290.318643] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3290.318645] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3290.318647] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3290.318650] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3290.318691] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3290.318694] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3290.318696] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3290.318698] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3290.318700] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3290.318702] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3290.318746] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3290.318749] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3290.318751] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3290.318753] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3290.318755] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3290.318758] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3290.318804] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3290.318807] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3290.318809] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3290.318811] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3290.318813] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3290.318815] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3290.318817] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3290.318859] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3290.318862] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3290.318864] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3290.636446] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3290.636447] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3290.636450] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3290.636451] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3290.636453] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3290.636455] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3290.636457] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3290.652694] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3290.652697] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3290.652700] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3290.652702] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3290.652706] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3290.652708] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3290.652712] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3290.652715] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3290.652717] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3290.652719] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3290.652721] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3290.652723] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3290.652725] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3290.652726] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3290.652728] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3290.669245] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3290.669281] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3290.669284] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3290.669286] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3290.669289] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3290.669293] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3290.669297] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3290.669299] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3290.669301] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3290.669303] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3290.669305] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3290.669328] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3290.669331] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3290.669333] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3290.669335] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3290.986253] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3290.986255] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3290.986257] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3290.986302] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3290.986305] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3290.986307] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3290.986309] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3290.986312] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3290.986316] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3290.986317] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3290.986369] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3290.986372] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3290.986373] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3290.986376] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3290.986381] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3290.986383] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3290.986385] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3290.986387] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3290.986389] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3291.002587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3291.002643] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3291.002665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3291.002668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3291.002670] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3291.002673] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3291.002675] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3291.002677] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3291.002679] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3291.002681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3291.002683] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3291.002686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3291.002688] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3291.002690] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3291.002692] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3291.002694] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3291.002696] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3291.002698] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3291.303370] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3291.303372] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3291.303374] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3291.303376] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3291.303378] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3291.303379] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3291.319512] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3291.319545] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3291.319548] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3291.319551] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3291.319553] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3291.319558] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3291.319561] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3291.319563] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3291.319566] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3291.319568] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3291.319570] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3291.319598] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3291.319601] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3291.319603] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3291.319605] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3291.319607] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3291.319610] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3291.319652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3291.319655] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3291.319657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3291.319659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3291.319662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3291.319664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3291.319708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3291.319710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3291.319712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3291.319715] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3291.319717] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3291.319719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3291.319763] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3291.653846] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3291.653887] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3291.653889] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3291.653892] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3291.653894] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3291.653896] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3291.653898] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3291.653942] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3291.653945] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3291.653947] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3291.653949] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3291.653952] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3291.653955] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3291.653957] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3291.654013] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3291.654015] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3291.654017] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3291.654018] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3291.654020] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3291.654022] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3291.654024] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3291.654026] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3291.654028] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3291.669906] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3291.669940] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3291.669942] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3291.669945] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3291.669948] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3291.669952] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3291.669955] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3291.669957] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3291.669959] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3291.669962] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3291.669964] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3291.669992] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3291.669995] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3291.669997] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3291.971687] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3291.986877] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3291.986932] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3291.986935] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3291.986950] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3291.986953] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3291.986956] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3291.986976] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3291.986979] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3291.986981] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3291.986984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3291.986986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3291.986989] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3291.986991] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3291.986994] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3291.986996] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3291.986998] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3291.987000] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3291.987002] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3291.987005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3291.987007] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3291.987009] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3291.987011] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3291.987013] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3291.987018] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3291.987020] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3291.987022] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3291.987024] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3291.987026] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3291.987028] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3291.987031] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3291.987033] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3291.987035] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3291.987036] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3291.987038] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3292.305118] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3292.305120] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3292.305122] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3292.305124] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3292.305173] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3292.305176] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3292.305178] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3292.305180] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3292.305183] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3292.305185] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3292.305226] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3292.305228] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3292.305231] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3292.305233] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3292.305235] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3292.305239] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3292.305240] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3292.305295] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3292.305297] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3292.305299] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3292.305301] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3292.305303] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3292.305305] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3292.305307] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3292.305308] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3292.305310] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3292.338234] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3292.338277] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3292.338294] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3292.667865] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3292.667867] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3292.667869] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3292.667871] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3292.667873] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3292.667920] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3292.667923] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3292.667925] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3292.667927] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3292.667929] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3292.667933] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3292.667934] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3292.667989] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3292.667991] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3292.667993] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3292.667995] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3292.667997] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3292.667999] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3292.668001] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3292.668003] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3292.668004] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3292.672272] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3292.672312] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3292.672330] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3292.672346] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3292.672349] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3292.672365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3292.672367] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3292.672383] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3292.672386] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3292.672407] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3292.672410] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3292.672414] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3292.672417] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3292.672420] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3292.672422] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3292.672424] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3292.971681] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3292.971683] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3292.971684] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3292.971686] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3292.988090] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3292.988126] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3292.988129] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3292.988131] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3292.988133] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3292.988136] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3292.988138] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3292.988140] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3292.988143] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3292.988145] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3292.988147] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3292.988152] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3292.988154] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3292.988156] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3292.988158] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3292.988160] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3292.988178] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3292.988182] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3292.988184] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3292.988186] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3292.988188] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3292.988190] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3292.988192] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3292.988238] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3292.988240] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3292.988242] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3292.988244] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3292.988246] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3292.988248] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3292.988299] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3292.988302] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3293.321921] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3293.321924] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3293.321926] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3293.321970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3293.321973] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3293.321975] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3293.321977] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3293.321980] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3293.321983] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3293.321985] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3293.322039] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3293.322041] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3293.322043] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3293.322045] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3293.322047] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3293.322049] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3293.322051] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3293.322053] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3293.322055] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3293.338274] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3293.338291] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3293.338293] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3293.338296] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3293.338316] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3293.338318] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3293.338321] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3293.338323] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3293.338325] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3293.338328] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3293.338330] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3293.338335] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3293.338337] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3293.338340] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3293.338342] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3293.338344] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3293.338346] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3293.338375] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3293.638810] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3293.638812] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3293.638814] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3293.638816] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3293.638818] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3293.638820] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3293.638822] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3293.638824] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3293.638826] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3293.638828] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3293.638830] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3293.638832] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3293.638834] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3293.638836] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3293.638838] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3293.638840] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3293.638842] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3293.638844] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3293.638846] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3293.638848] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3293.638850] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3293.638852] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3293.638854] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3293.638857] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3293.638858] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3293.638860] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3293.638862] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3293.638864] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3293.638867] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3293.638869] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3293.638870] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3293.638872] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3293.638874] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3293.638876] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3293.655234] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3293.655293] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3293.655315] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3293.655318] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3293.655320] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3293.989320] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3293.989321] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3293.989377] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3293.989380] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3293.989381] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3293.989383] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3293.989385] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3293.989387] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3293.989389] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3293.989391] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3293.989392] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3294.005632] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3294.005691] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3294.005694] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3294.005696] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3294.005698] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3294.005701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3294.005703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3294.005705] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3294.005707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3294.005709] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3294.005712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3294.005714] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3294.005716] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3294.005718] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3294.005720] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3294.005722] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3294.005724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3294.005726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3294.005728] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3294.005730] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3294.005732] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3294.005734] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3294.005736] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3294.005738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3294.005740] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3294.005742] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3294.306263] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3294.306308] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3294.306310] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3294.306312] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3294.306315] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3294.306317] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3294.306320] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3294.306322] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3294.306375] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3294.306377] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3294.306379] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3294.306381] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3294.306383] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3294.306384] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3294.306386] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3294.306388] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3294.306390] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3294.326747] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3294.326761] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3294.326764] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3294.326784] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3294.326786] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3294.326791] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3294.326794] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3294.326797] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3294.326799] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3294.326802] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3294.326804] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3294.326838] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3294.326841] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3294.326843] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3294.326845] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3294.326848] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3294.326850] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3294.326893] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3294.326908] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3294.326911] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3294.656426] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3294.656437] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3294.656474] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3294.656476] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3294.656479] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3294.656481] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3294.656483] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3294.656485] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3294.656487] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3294.656528] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3294.656531] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3294.656533] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3294.656535] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3294.656537] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3294.656539] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3294.656541] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3294.656543] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3294.656586] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3294.656589] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3294.656591] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3294.656593] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3294.656595] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3294.656598] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3294.656644] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3294.656647] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3294.656649] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3294.656651] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3294.656654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3294.656657] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3294.656658] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3294.656712] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3294.656714] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3294.656716] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3294.656718] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3294.656720] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3294.656722] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3294.656723] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3294.656725] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3294.656727] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3294.672966] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3294.673049] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3294.973411] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3294.973413] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3294.973458] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3294.973460] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3294.973462] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3294.973465] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3294.973467] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3294.973469] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3294.973471] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3294.973515] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3294.973518] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3294.973520] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3294.973625] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3294.989995] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3294.990017] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3294.990030] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3294.990032] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3294.990035] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3294.990037] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3294.990039] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3294.990041] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3294.990043] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3294.990045] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3294.990047] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3294.990049] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3294.990050] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3294.990052] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3294.990054] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3294.990056] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3294.990058] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3294.990060] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3294.990061] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3294.990063] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3294.990065] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3294.990067] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3294.990069] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3294.990071] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3294.990073] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3294.990075] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3295.340384] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3295.340392] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3295.340408] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3295.340410] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3295.340412] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3295.340414] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3295.340416] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3295.340418] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3295.340420] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3295.340421] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3295.340423] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3295.340425] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3295.340427] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3295.340429] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3295.340430] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3295.340432] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3295.340434] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3295.340436] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3295.340438] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3295.340440] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3295.340442] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3295.340444] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3295.340446] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3295.340448] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3295.340450] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3295.340452] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3295.340456] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3295.340457] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3295.340459] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3295.340461] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3295.340462] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3295.340464] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3295.340466] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3295.340468] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3295.340470] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3295.340471] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3295.340473] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3295.356969] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3295.356988] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3295.641826] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3295.641829] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3295.641831] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3295.641833] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3295.641835] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3295.641837] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3295.641839] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3295.641841] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3295.641882] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3295.641886] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3295.641888] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3295.641896] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3295.641943] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3295.641953] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3295.657200] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3295.657239] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3295.657241] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3295.657273] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3295.657275] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3295.657279] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3295.657289] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3295.657290] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3295.657339] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3295.657341] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3295.657344] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3295.657400] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3295.657444] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3295.657446] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3295.657448] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3295.657500] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3295.657502] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3295.657504] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3295.657506] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3295.657508] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3295.657510] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3295.657512] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3295.657514] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3295.657554] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3295.657557] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3296.008724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3296.008769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3296.008772] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3296.008774] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3296.008776] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3296.008778] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3296.008780] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3296.008825] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3296.008827] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3296.008830] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3296.008832] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3296.008834] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3296.008836] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3296.008838] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3296.008880] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3296.008883] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3296.008885] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3296.008887] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3296.008888] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3296.008890] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3296.008892] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3296.008894] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3296.008936] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3296.008938] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3296.008940] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3296.008943] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3296.008945] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3296.008947] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3296.008993] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3296.008996] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3296.008998] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3296.009001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3296.009003] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3296.009007] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3296.009009] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3296.009063] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3296.009066] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3296.009067] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3296.009069] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3296.338016] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3296.338062] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3296.338065] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3296.338068] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3296.338070] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3296.338072] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3296.338074] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3296.338077] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3296.338119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3296.338121] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3296.338123] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3296.338126] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3296.338128] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3296.338129] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3296.338131] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3296.338133] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3296.338174] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3296.338177] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3296.338179] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3296.338181] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3296.338183] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3296.338185] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3296.338231] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3296.338233] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3296.338235] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3296.338237] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3296.338240] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3296.338243] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3296.338245] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3296.338300] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3296.338303] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3296.338304] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3296.338306] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3296.338308] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3296.338310] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3296.338312] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3296.338314] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3296.338316] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3296.347219] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3296.347231] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3296.675169] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3296.675172] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3296.675174] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3296.675176] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3296.675178] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3296.675221] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3296.675224] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3296.675225] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3296.675227] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3296.675230] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3296.675232] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3296.675234] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3296.675236] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3296.675277] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3296.675280] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3296.675282] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3296.675284] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3296.675287] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3296.675289] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3296.675332] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3296.675334] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3296.675337] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] d^C
130|console:/ #

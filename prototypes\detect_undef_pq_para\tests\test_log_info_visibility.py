"""
Test for Log File Information section visibility

This test verifies that the "Log File Information" section is correctly
hidden for Grid Comparison Plotter and Firmware Log Visualizer, but shown
for PQ Config Validator.
"""

import pytest
import re
from pathlib import Path

class TestLogInfoVisibility:
    """Test Log File Information section visibility in JavaScript frontend"""
    
    def test_javascript_condition_logic(self):
        """Test that the JavaScript condition correctly hides log info for the right processors"""
        
        # Read the JavaScript file
        js_file_path = Path("static/js/app.js")
        assert js_file_path.exists(), "JavaScript file not found"
        
        with open(js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Find the log info condition
        log_info_pattern = r"if\s*\(\s*results\.log_info\s*&&\s*results\.processor_used\s*!==\s*['\"]grid_comparison['\"]\s*&&\s*results\.processor_used\s*!==\s*['\"]firmware_log_visualizer['\"]\s*\)"
        
        matches = re.search(log_info_pattern, js_content)
        assert matches is not None, "Log info condition not found or incorrect in JavaScript"
        
        print("✓ JavaScript condition correctly excludes both grid_comparison and firmware_log_visualizer")
    
    def test_comment_accuracy(self):
        """Test that the comment accurately describes the condition"""
        
        js_file_path = Path("static/js/app.js")
        with open(js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Find the comment before the condition
        comment_pattern = r"//\s*Display log file information \(skip for Grid Comparison Plotter and Firmware Log Visualizer\)"
        
        matches = re.search(comment_pattern, js_content)
        assert matches is not None, "Comment not found or incorrect in JavaScript"
        
        print("✓ Comment accurately describes the exclusion of both applications")
    
    def test_processor_exclusion_logic(self):
        """Test the logical behavior of processor exclusion"""
        
        # Simulate the JavaScript condition logic in Python
        def should_show_log_info(processor_used, has_log_info=True):
            """Simulate the JavaScript condition"""
            return has_log_info and processor_used != 'grid_comparison' and processor_used != 'firmware_log_visualizer'
        
        # Test cases
        test_cases = [
            # (processor_used, has_log_info, expected_result, description)
            ('grid_comparison', True, False, 'Grid Comparison Plotter should hide log info'),
            ('firmware_log_visualizer', True, False, 'Firmware Log Visualizer should hide log info'),
            ('pq_config_validator', True, True, 'PQ Config Validator should show log info'),
            ('unknown_processor', True, True, 'Unknown processor should show log info'),
            ('grid_comparison', False, False, 'No log info available should hide section'),
            ('firmware_log_visualizer', False, False, 'No log info available should hide section'),
            ('pq_config_validator', False, False, 'No log info available should hide section'),
        ]
        
        for processor, has_log_info, expected, description in test_cases:
            result = should_show_log_info(processor, has_log_info)
            assert result == expected, f"Failed: {description} (got {result}, expected {expected})"
            print(f"✓ {description}")
    
    def test_ui_behavior_test_file_updated(self):
        """Test that the UI behavior test file has been updated correctly"""
        
        test_file_path = Path("test_ui_behavior.html")
        assert test_file_path.exists(), "UI behavior test file not found"
        
        with open(test_file_path, 'r', encoding='utf-8') as f:
            test_content = f.read()
        
        # Check that the test file has the updated condition
        condition_pattern = r"results\.processor_used\s*!==\s*['\"]grid_comparison['\"]\s*&&\s*results\.processor_used\s*!==\s*['\"]firmware_log_visualizer['\"]\s*"
        assert re.search(condition_pattern, test_content), "UI test file condition not updated"
        
        # Check that it has test cases for all three applications
        assert 'Grid Comparison Plotter' in test_content, "Grid Comparison Plotter test case missing"
        assert 'Firmware Log Visualizer' in test_content, "Firmware Log Visualizer test case missing"
        assert 'PQ Config Validator' in test_content, "PQ Config Validator test case missing"
        
        # Check that the expectations are correct
        assert 'should hide log info' in test_content, "Hide log info expectation missing"
        assert 'should show log info' in test_content, "Show log info expectation missing"
        
        print("✓ UI behavior test file correctly updated with all test cases")
    
    def test_template_consistency(self):
        """Test that upload template hints are consistent with the new behavior"""
        
        template_path = Path("templates/upload.html")
        assert template_path.exists(), "Upload template not found"
        
        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # Check that firmware_log_visualizer has its own specific hint
        assert 'firmware_log_visualizer' in template_content, "Firmware log visualizer not found in template"
        assert 'Dolby debug logs' in template_content, "Firmware log specific hint missing"
        
        # Check that grid_comparison_plotter has TC Flash specific hint
        assert 'Config and DM values sections' in template_content, "TC Flash specific hint missing"
        
        print("✓ Upload template has appropriate hints for each application type")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

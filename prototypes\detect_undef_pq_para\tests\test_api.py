#!/usr/bin/env python3
"""
API endpoint tests for Web Application Selection Feature
Author: <PERSON> <<EMAIL>>
"""

import sys
import json
from pathlib import Path

# Add parent directory to path to import local modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from main import session_manager
from web_app_registry import web_app_registry
from .conftest import TestFixtures, TestValidators, TestConstants

def test_session_creation():
    """Test session creation functionality"""
    print("Testing Session Creation...")
    
    # Test creating session with valid data
    session_id = session_manager.create_session("test_user", "pq_config_validator")
    assert session_id is not None, "Session creation should return session ID"
    assert len(session_id) > 0, "Session ID should not be empty"
    
    # Verify session data
    session_data = session_manager.get_session(session_id)
    assert session_data is not None, "Session should be retrievable"
    TestValidators.validate_session_data(session_data)
    assert session_data["user_id"] == "test_user", "User ID should match"
    assert session_data["selected_app"] == "pq_config_validator", "Selected app should match"
    
    print(f"   [OK] Session created successfully: {session_id}")

def test_session_retrieval():
    """Test session retrieval functionality"""
    print("Testing Session Retrieval...")
    
    # Create test session
    session_id = session_manager.create_session("test_user_2", "pq_config_validator")
    
    # Test retrieving existing session
    session_data = session_manager.get_session(session_id)
    assert session_data is not None, "Should retrieve existing session"
    assert session_data["user_id"] == "test_user_2", "User ID should match"
    
    # Test retrieving non-existent session
    fake_session_id = "00000000-0000-0000-0000-000000000000"
    session_data = session_manager.get_session(fake_session_id)
    assert session_data is None, "Should return None for non-existent session"
    
    print("   [OK] Session retrieval works correctly")

def test_session_validation():
    """Test session validation functionality"""
    print("Testing Session Validation...")

    # Create test session
    session_id = session_manager.create_session("test_user_3", "pq_config_validator")

    # Test validating existing session (using get_session method)
    session_data = session_manager.get_session(session_id)
    is_valid = session_data is not None
    assert is_valid == True, "Valid session should return True"

    # Test validating non-existent session
    fake_session_id = "11111111-1111-1111-1111-111111111111"
    session_data = session_manager.get_session(fake_session_id)
    is_valid = session_data is not None
    assert is_valid == False, "Invalid session should return False"

    # Test validating empty/None session ID
    try:
        session_data = session_manager.get_session("")
        is_valid = session_data is not None
        assert is_valid == False, "Empty session ID should return False"
    except:
        # If get_session throws an exception for empty string, that's also valid
        pass

    try:
        session_data = session_manager.get_session(None)
        is_valid = session_data is not None
        assert is_valid == False, "None session ID should return False"
    except:
        # If get_session throws an exception for None, that's also valid
        pass

    print("   [OK] Session validation works correctly")

def test_user_registration_logic():
    """Test user registration logic"""
    print("Testing User Registration Logic...")
    
    # Test valid user registration data
    valid_registrations = [
        {"user_id": "test_user_1", "selected_app": "pq_config_validator"},
        {"user_id": "test_user_2", "selected_app": "grid_comparison_plotter"},
        {"user_id": "test_user_3", "selected_app": "firmware_log_visualizer"}
    ]
    
    for registration in valid_registrations:
        # Validate user ID
        user_id = registration["user_id"]
        assert len(user_id) > 0, f"User ID should not be empty: {user_id}"
        assert user_id.replace("_", "").replace("-", "").isalnum(), f"User ID should be alphanumeric: {user_id}"
        
        # Validate selected app
        selected_app = registration["selected_app"]
        assert web_app_registry.is_valid_application(selected_app), f"Selected app should be valid: {selected_app}"
        
        # Test session creation
        session_id = session_manager.create_session(user_id, selected_app)
        assert session_id is not None, f"Session creation should succeed for {user_id}"
        
        print(f"   [OK] Registration valid for {user_id} -> {selected_app}")
    
    # Test invalid user registration data
    invalid_registrations = [
        {"user_id": "", "selected_app": "pq_config_validator"},
        {"user_id": "test_user", "selected_app": ""},
        {"user_id": "test_user", "selected_app": "nonexistent_app"},
        {"user_id": "<EMAIL>", "selected_app": "pq_config_validator"}
    ]
    
    for registration in invalid_registrations:
        user_id = registration["user_id"]
        selected_app = registration["selected_app"]
        
        # Check if user ID is valid
        user_id_valid = len(user_id) > 0 and user_id.replace("_", "").replace("-", "").isalnum()
        
        # Check if app is valid
        app_valid = web_app_registry.is_valid_application(selected_app)
        
        if not user_id_valid or not app_valid:
            print(f"   [OK] Registration correctly invalid for {user_id} -> {selected_app}")
        else:
            print(f"   [WARN] Registration unexpectedly valid for {user_id} -> {selected_app}")

def test_web_applications_api_data():
    """Test web applications API data structure"""
    print("Testing Web Applications API Data...")
    
    # Get enabled applications
    apps = web_app_registry.get_enabled_applications()
    
    # Convert to API format
    api_data = {
        "applications": [
            {
                "id": app.id,
                "name": app.name,
                "description": app.description,
                "icon": app.icon,
                "author": app.author,
                "version": app.version
            }
            for app in apps
        ]
    }
    
    # Validate API response structure
    TestValidators.validate_api_response(api_data, ["applications"])
    
    # Validate each application in the response
    for app_data in api_data["applications"]:
        required_fields = ["id", "name", "description", "icon", "author", "version"]
        TestValidators.validate_api_response(app_data, required_fields)
        
        # Validate field types and content
        assert isinstance(app_data["id"], str) and len(app_data["id"]) > 0, "ID should be non-empty string"
        assert isinstance(app_data["name"], str) and len(app_data["name"]) > 0, "Name should be non-empty string"
        assert isinstance(app_data["description"], str) and len(app_data["description"]) > 0, "Description should be non-empty string"
        assert isinstance(app_data["icon"], str) and len(app_data["icon"]) > 0, "Icon should be non-empty string"
        assert isinstance(app_data["author"], str) and len(app_data["author"]) > 0, "Author should be non-empty string"
        assert isinstance(app_data["version"], str) and len(app_data["version"]) > 0, "Version should be non-empty string"
    
    print(f"   [OK] API data structure valid for {len(api_data['applications'])} applications")

def test_url_generation_for_apps():
    """Test URL generation for different application types"""
    print("Testing URL Generation for Applications...")
    
    test_user = "test_user_url"
    
    # Test local application (PQ Config Validator)
    pq_url = web_app_registry.get_application_url("pq_config_validator", test_user)
    assert pq_url == "/", "PQ Config Validator should return local URL"
    
    # Test external applications
    external_apps = ["image_processor", "data_analyzer"]
    for app_id in external_apps:
        if web_app_registry.is_valid_application(app_id):
            url = web_app_registry.get_application_url(app_id, test_user)
            assert url is not None, f"External app {app_id} should return URL"
            assert url.startswith("http"), f"External app {app_id} should return HTTP URL"
            print(f"   [OK] {app_id} -> {url}")
    
    # Test invalid application
    invalid_url = web_app_registry.get_application_url("nonexistent_app", test_user)
    assert invalid_url is None, "Invalid app should return None URL"
    
    print("   [OK] URL generation works correctly for all application types")

def test_session_app_tracking():
    """Test session tracking of selected applications"""
    print("Testing Session App Tracking...")
    
    # Create sessions with different applications
    test_cases = [
        ("user_pq", "pq_config_validator"),
        ("user_img", "image_processor"),
        ("user_data", "data_analyzer")
    ]
    
    session_ids = []
    for user_id, app_id in test_cases:
        if web_app_registry.is_valid_application(app_id):
            session_id = session_manager.create_session(user_id, app_id)
            session_ids.append((session_id, user_id, app_id))
    
    # Verify each session tracks the correct application
    for session_id, expected_user, expected_app in session_ids:
        session_data = session_manager.get_session(session_id)
        assert session_data is not None, f"Session {session_id} should exist"
        assert session_data["user_id"] == expected_user, f"User ID should match: {expected_user}"
        assert session_data["selected_app"] == expected_app, f"Selected app should match: {expected_app}"
        print(f"   [OK] Session {session_id[:8]}... tracks {expected_user} -> {expected_app}")
    
    print("   [OK] Session app tracking works correctly")

if __name__ == "__main__":
    print("=" * 60)
    print("Web Application Selection API Tests")
    print("=" * 60)
    
    try:
        test_session_creation()
        test_session_retrieval()
        test_session_validation()
        test_user_registration_logic()
        test_web_applications_api_data()
        test_url_generation_for_apps()
        test_session_app_tracking()
        
        print("=" * 60)
        print("All API tests passed! [OK]")
        print("=" * 60)
        
    except Exception as e:
        print(f"API test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

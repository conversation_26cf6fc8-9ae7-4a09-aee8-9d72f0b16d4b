"""
Test for Firmware Log Visualizer workflow

This test verifies that the Firmware Log Visualizer application correctly
processes firmware log files and generates PNG visualizations.
"""

import pytest
from unittest.mock import patch, MagicMock
from pathlib import Path
import tempfile
import json

from main import process_log_file
from log_processors import LogProcessingResult, LogProcessorManager
from file_type_detector import FileRouter

class TestFirmwareLogWorkflow:
    """Test Firmware Log Visualizer end-to-end workflow"""
    
    def test_firmware_log_routing(self):
        """Test that firmware log files are correctly routed to log processing when firmware_log_visualizer is selected"""
        
        # Create a test firmware log file
        firmware_log_content = """
L17-IC-filter_0.0416.txt firmware log content
Debug output from Dolby Vision processing

dBrightness(100)
dContrast(85)
dSaturation(90)
dBacklight(75)

gainPos_precisionRendering(50)
gainPos_dLocalContrast(60)
gainPos_dBrightness(45)

confidence(95)
precisionRenderingStrength(80)

mid_boost: 40
highlight_stretch: 35
shadow_drop: 20

UpMappingStrength= 70
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(firmware_log_content)
            temp_file_path = Path(f.name)
        
        try:
            # Test 1: Verify that this file is NOT detected as a TC Flash log
            assert not FileRouter.should_use_log_processing(temp_file_path), \
                "Firmware log should not be detected as TC Flash log"
            
            # Test 2: Mock the firmware log visualizer execution
            mock_result = LogProcessingResult(
                success=True,
                message="Firmware log visualization generated successfully",
                processor_used="firmware_log_visualizer",
                output_files=["L17-IC-filter_0.0416.png", "summary.csv"],
                static_files=[{
                    "filename": "L17-IC-filter_0.0416.png",
                    "unique_filename": "test_firmware_viz.png",
                    "url": "/static/images/firmware_log_visualizer/test_firmware_viz.png"
                }]
            )
            
            with patch('log_processors.SubprocessLogProcessor.execute_firmware_log_visualizer') as mock_execute:
                mock_execute.return_value = mock_result
                
                # Test 3: Process the file with firmware_log_visualizer selection
                result = LogProcessorManager.process_log_file(
                    temp_file_path, 
                    Path("/tmp/test_output"), 
                    "firmware_log_visualizer"
                )
                
                # Verify the result
                assert result.success == True
                assert result.processor_used == "firmware_log_visualizer"
                assert "L17-IC-filter_0.0416.png" in result.output_files
                assert len(result.static_files) > 0
                
                # Verify the mock was called
                mock_execute.assert_called_once()
                
                print("✓ Firmware log file correctly processed with firmware_log_visualizer")
                print(f"✓ Generated files: {result.output_files}")
                print(f"✓ Static files: {[f['url'] for f in result.static_files]}")
                
        finally:
            # Cleanup
            temp_file_path.unlink(missing_ok=True)
    
    def test_grid_comparison_tc_flash_validation(self):
        """Test that Grid Comparison Plotter still requires TC Flash log format"""
        
        # Create a firmware log file (not TC Flash format)
        firmware_log_content = """
L17-IC-filter_0.0416.txt firmware log content
dBrightness(100)
dContrast(85)
gainPos_precisionRendering(50)
mid_boost: 40
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(firmware_log_content)
            temp_file_path = Path(f.name)
        
        try:
            # Test that grid_comparison rejects firmware log files
            result = LogProcessorManager.process_log_file(
                temp_file_path, 
                Path("/tmp/test_output"), 
                "grid_comparison"
            )
            
            # Should fail because firmware log doesn't have TC Flash format
            assert result.success == False
            assert "TC Flash PQ log file" in result.message
            
            print("✓ Grid Comparison Plotter correctly rejects non-TC Flash files")
            
        finally:
            # Cleanup
            temp_file_path.unlink(missing_ok=True)
    
    def test_tc_flash_log_with_firmware_visualizer(self):
        """Test that Firmware Log Visualizer can process TC Flash logs too"""
        
        # Create a TC Flash log file
        tc_flash_content = """
fallback: 0
Config values:
intensity_level: 100
gain_red: 50
gain_green: 60
gain_blue: 70

DM values:
LocalMappingStrength 80
UpMappingStrength 90

fallback: 0
Config values:
intensity_level: 105
gain_red: 55
gain_green: 65
gain_blue: 75
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(tc_flash_content)
            temp_file_path = Path(f.name)
        
        try:
            # Mock the firmware log visualizer execution
            mock_result = LogProcessingResult(
                success=True,
                message="Firmware log visualization generated successfully",
                processor_used="firmware_log_visualizer",
                output_files=["test_tc_flash.png", "summary.csv"],
                static_files=[{
                    "filename": "test_tc_flash.png",
                    "unique_filename": "test_tc_flash_viz.png",
                    "url": "/static/images/firmware_log_visualizer/test_tc_flash_viz.png"
                }]
            )
            
            with patch('log_processors.SubprocessLogProcessor.execute_firmware_log_visualizer') as mock_execute:
                mock_execute.return_value = mock_result
                
                # Test that firmware_log_visualizer can process TC Flash logs
                result = LogProcessorManager.process_log_file(
                    temp_file_path, 
                    Path("/tmp/test_output"), 
                    "firmware_log_visualizer"
                )
                
                # Should succeed
                assert result.success == True
                assert result.processor_used == "firmware_log_visualizer"
                
                print("✓ Firmware Log Visualizer can process TC Flash logs")
                
        finally:
            # Cleanup
            temp_file_path.unlink(missing_ok=True)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

{"tests/test_log_processing.py::TestLogParser::test_parse_valid_log": true, "tests/test_log_processing.py::TestLogParser::test_parse_empty_log": true, "tests/test_log_processing.py::TestLogProcessors::test_grid_comparison_processor": true, "tests/test_log_processing.py::TestLogProcessors::test_log_parser_processor": true, "tests/test_log_processing.py::TestLogProcessorManager::test_detect_best_processor": true, "tests/test_log_processing.py::TestLogProcessorManager::test_process_invalid_file": true, "tests/test_subprocess_architecture.py::TestFileTypeDetector::test_processing_mode_routing": true, "tests/test_grid_comparison_ui.py": true}
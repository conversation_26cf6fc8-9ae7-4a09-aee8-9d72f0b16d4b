["tests/test_api.py::test_session_app_tracking", "tests/test_api.py::test_session_creation", "tests/test_api.py::test_session_retrieval", "tests/test_api.py::test_session_validation", "tests/test_api.py::test_url_generation_for_apps", "tests/test_api.py::test_user_registration_logic", "tests/test_api.py::test_web_applications_api_data", "tests/test_download.py::test_download_functionality", "tests/test_firmware_log_workflow.py::TestFirmwareLogWorkflow::test_firmware_log_routing", "tests/test_firmware_log_workflow.py::TestFirmwareLogWorkflow::test_grid_comparison_tc_flash_validation", "tests/test_firmware_log_workflow.py::TestFirmwareLogWorkflow::test_tc_flash_log_with_firmware_visualizer", "tests/test_log_info_visibility.py::TestLogInfoVisibility::test_comment_accuracy", "tests/test_log_info_visibility.py::TestLogInfoVisibility::test_javascript_condition_logic", "tests/test_log_info_visibility.py::TestLogInfoVisibility::test_processor_exclusion_logic", "tests/test_log_info_visibility.py::TestLogInfoVisibility::test_template_consistency", "tests/test_log_info_visibility.py::TestLogInfoVisibility::test_ui_behavior_test_file_updated", "tests/test_log_processing.py::TestFileTypeDetector::test_detect_pq_config", "tests/test_log_processing.py::TestFileTypeDetector::test_detect_tc_flash_log", "tests/test_log_processing.py::TestFileTypeDetector::test_get_processing_mode", "tests/test_log_processing.py::TestIntegratedWorkflow::test_complete_log_processing_workflow", "tests/test_log_processing.py::TestLogParser::test_parse_empty_log", "tests/test_log_processing.py::TestLogParser::test_parse_valid_log", "tests/test_log_processing.py::TestLogProcessorManager::test_detect_best_processor", "tests/test_log_processing.py::TestLogProcessorManager::test_process_invalid_file", "tests/test_log_processing.py::TestLogProcessors::test_grid_comparison_processor", "tests/test_log_processing.py::TestLogProcessors::test_log_parser_processor", "tests/test_log_processing.py::TestTCFlashLogDetector::test_empty_file", "tests/test_log_processing.py::TestTCFlashLogDetector::test_invalid_log_file", "tests/test_log_processing.py::TestTCFlashLogDetector::test_nonexistent_file", "tests/test_log_processing.py::TestTCFlashLogDetector::test_valid_tc_flash_log", "tests/test_registry.py::test_enable_disable_application", "tests/test_registry.py::test_get_all_applications", "tests/test_registry.py::test_get_application", "tests/test_registry.py::test_get_application_url", "tests/test_registry.py::test_get_enabled_applications", "tests/test_registry.py::test_is_valid_application", "tests/test_registry.py::test_new_bin_applications", "tests/test_registry.py::test_real_config_applications", "tests/test_registry.py::test_register_application", "tests/test_registry.py::test_registry_initialization", "tests/test_subprocess_architecture.py::TestFileTypeDetector::test_processing_mode_routing", "tests/test_subprocess_architecture.py::TestFileTypeDetector::test_processor_recommendation", "tests/test_subprocess_architecture.py::TestFileTypeDetector::test_tc_flash_log_detection", "tests/test_subprocess_architecture.py::TestLogProcessorManager::test_process_with_user_selection_firmware_log_visualizer", "tests/test_subprocess_architecture.py::TestLogProcessorManager::test_process_with_user_selection_grid_comparison", "tests/test_subprocess_architecture.py::TestLogProcessorManager::test_process_with_user_selection_log_parser", "tests/test_subprocess_architecture.py::TestSubprocessLogProcessor::test_execute_firmware_log_visualizer", "tests/test_subprocess_architecture.py::TestSubprocessLogProcessor::test_execute_grid_comparison_plotter", "tests/test_subprocess_architecture.py::TestSubprocessLogProcessor::test_execute_log_parser_plotter", "tests/test_subprocess_architecture.py::TestSubprocessLogProcessor::test_get_bin_directory", "tests/test_subprocess_architecture.py::TestTCFlashLogDetector::test_invalid_log_file", "tests/test_subprocess_architecture.py::TestTCFlashLogDetector::test_nonexistent_file", "tests/test_subprocess_architecture.py::TestTCFlashLogDetector::test_valid_tc_flash_log", "tests/test_subprocess_architecture.py::TestWebApplicationRegistry::test_application_properties", "tests/test_subprocess_architecture.py::TestWebApplicationRegistry::test_get_enabled_applications", "tests/test_web_app_selection.py::test_config", "tests/test_web_app_selection.py::test_json_serialization", "tests/test_web_app_selection.py::test_web_app_registry", "tests/test_workflow.py::test_complete_workflow", "tests/test_workflow.py::test_english_only_ui", "tests/test_workflow.py::test_persistence_simulation"]
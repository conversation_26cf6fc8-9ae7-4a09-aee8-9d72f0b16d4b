
130|console:/ #
130|console:/ # su
ys/bus/rpmsg/devices/virtio*.pqu-vuart.-*/mtk_dbg/record_log                  <
console:/ # dmesg -w | grep DolbyDBG
[ 3491.151066] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3491.151068] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3491.151070] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3491.151072] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3491.151074] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3491.151076] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3491.151118] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.151120] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.151123] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.151125] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.151127] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.151129] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.151174] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.151177] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.151179] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.151181] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.151183] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3491.151187] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3491.151188] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3491.151229] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3491.151231] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3491.151233] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3491.151235] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3491.151237] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3491.151239] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3491.151241] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3491.151243] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3491.151244] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3491.151290] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.151292] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.151294] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.151296] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.151343] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.151346] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.151348] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.151350] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.151352] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.151355] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.151399] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.151401] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.151403] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.151405] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.151408] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.151410] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.151455] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.151457] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.151459] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.151461] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.151464] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.151466] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.151510] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.151513] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.151515] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.151517] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.151519] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.151521] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.151567] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.151570] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.151572] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3491.151574] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3491.151576] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3491.151578] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3491.151580] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3491.151623] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3491.151625] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3491.151627] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3491.151630] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3491.151632] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3491.151634] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3491.151636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3491.151638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3491.151679] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3491.151681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.151683] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.151686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.151688] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.151690] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.151735] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.151738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.151741] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.151743] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.151745] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.151747] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3491.151793] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3491.151795] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3491.151797] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3491.151814] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3491.151817] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3491.151819] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3491.151820] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3491.151822] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3491.151824] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3491.151826] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3491.151829] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3491.186726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.186760] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.186763] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.186765] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.186768] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.186772] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.186775] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.186777] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.186779] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.186781] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.186784] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.186812] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.186815] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.186817] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.186820] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.186822] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.186824] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.186867] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.186870] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.186872] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.186875] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.186877] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.186879] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.186924] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.186927] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.186929] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.186931] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.186933] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.186935] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.186981] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.186984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3491.186986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3491.186988] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3491.186991] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3491.186993] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3491.186994] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3491.187036] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3491.187038] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3491.187040] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3491.187042] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3491.187044] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3491.187046] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3491.187048] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3491.187050] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3491.187094] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.187097] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.187099] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.187101] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.187103] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.187106] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.187148] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.187151] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.187153] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.187155] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.187158] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3491.187161] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3491.187163] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3491.187218] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3491.187220] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3491.187222] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3491.187224] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3491.187226] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3491.187228] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3491.187229] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3491.187231] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3491.187233] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3491.202790] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.202868] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.202871] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.202873] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.202876] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.202878] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.202880] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.202882] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.202884] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.202887] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.202889] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.202891] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.202893] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.202895] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.202897] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.202899] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.202901] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.202903] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.202905] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.202907] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.202909] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.202911] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.202913] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.202915] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.202917] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.202919] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.202921] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.202923] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.202925] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.202929] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.202932] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3491.202934] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3491.202936] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3491.202938] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3491.202940] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3491.202942] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3491.202943] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3491.202945] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3491.202947] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3491.202949] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3491.202951] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3491.202952] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3491.202954] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3491.202956] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3491.202958] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.202960] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.202962] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.202964] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.202966] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.202968] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.202970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.202972] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.202974] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.202976] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.202978] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3491.202981] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3491.202983] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3491.202985] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3491.202986] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3491.202988] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3491.202990] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3491.202992] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3491.202993] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3491.202995] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3491.202997] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3491.202999] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3491.219465] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.219501] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.219504] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.219506] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.219509] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.219514] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.219516] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.219519] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.219521] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.219524] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.219542] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.219545] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.219548] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.219550] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.219552] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.219555] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.219556] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.219601] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.219604] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.219606] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.219609] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.219611] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.219613] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.219656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.219659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.219661] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.219664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.219666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.219668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.219714] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.219717] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3491.219719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3491.219721] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3491.219723] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3491.219725] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3491.219727] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3491.219769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3491.219771] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3491.219773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3491.219775] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3491.219777] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3491.219779] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3491.219781] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3491.219783] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3491.219824] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.219826] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.219829] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.219831] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.219833] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.219835] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.219880] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.219882] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.219885] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.219887] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.219889] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3491.219893] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3491.219895] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3491.219949] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3491.219951] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3491.219953] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3491.219955] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3491.219957] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3491.219959] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3491.219960] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3491.219962] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3491.219964] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3491.236165] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.236200] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.236203] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.236205] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.236207] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.236212] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.236215] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.236217] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.236219] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.236221] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.236224] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.236249] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.236252] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.236254] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.236256] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.236259] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.236261] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.236302] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.236305] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.236307] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.236309] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.236311] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.236313] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.236361] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.236364] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.236366] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.236369] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.236371] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.236373] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.236414] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.236416] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3491.236418] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3491.236421] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3491.236422] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3491.236425] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3491.236427] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3491.236469] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3491.236472] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3491.236474] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3491.236476] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3491.236478] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3491.236480] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3491.236482] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3491.236484] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3491.236526] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.236529] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.236531] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.236533] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.236535] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.236537] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.236582] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.236585] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.236587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.236590] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.236592] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3491.236595] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3491.236597] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3491.236652] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3491.236655] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3491.236656] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3491.236658] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3491.236661] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3491.236662] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3491.236664] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3491.236666] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3491.236668] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3491.252920] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.252922] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.252925] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.252927] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.252930] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.252959] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.252962] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.252964] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.252966] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.252969] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.252971] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.253012] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.253015] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.253017] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.253019] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.253022] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.253024] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.253130] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.253134] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.253136] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.253138] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.253141] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.253143] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.253147] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.253149] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3491.253151] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3491.253153] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3491.253156] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3491.253158] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3491.253160] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3491.253187] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3491.253190] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3491.253191] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3491.253194] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3491.253196] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3491.253197] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3491.253200] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3491.253201] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3491.253242] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.253245] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.253247] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.253250] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.253252] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.253254] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.253297] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.253299] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.253302] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.253304] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.253306] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3491.253310] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3491.253312] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3491.253366] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3491.253368] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3491.253370] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3491.253373] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3491.253375] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3491.253377] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3491.253379] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3491.253380] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3491.253382] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3491.269482] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.269525] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.269541] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.269543] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.269565] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.269567] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.269569] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.269572] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.269574] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.269576] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.269579] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.269581] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.269583] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.269585] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.269587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.269589] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.269591] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.269593] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.269595] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.269597] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.269599] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.269601] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.269603] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.269608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.269610] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.269612] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.269614] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.269616] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.269618] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.269620] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.269622] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3491.269624] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3491.269626] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3491.269628] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3491.269630] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3491.269632] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3491.269634] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3491.269636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3491.269638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3491.269639] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3491.269641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3491.269643] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3491.269645] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3491.269647] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3491.269649] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.269651] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.269653] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.269655] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.269656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.269658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.269660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.269662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.269664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.269666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.269668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3491.269672] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3491.269673] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3491.269675] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3491.269677] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3491.269678] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3491.269680] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3491.269682] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3491.269684] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3491.269685] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3491.269687] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3491.269689] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3491.287041] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.287056] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.287058] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.287061] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.287063] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.287065] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.287083] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.287086] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.287088] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.287090] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.287093] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.287095] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.287097] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.287099] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.287102] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.287104] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.287106] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.287108] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.287111] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.287113] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.287115] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.287117] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.287119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.287121] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.287123] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.287125] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.287126] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.287128] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.287130] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.287132] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.287134] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3491.287136] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3491.287138] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3491.287140] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3491.287142] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3491.287144] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3491.287146] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3491.287147] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3491.287149] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3491.287151] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3491.287153] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3491.287155] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3491.287157] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3491.287158] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3491.287160] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.287162] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.287164] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.287166] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.287168] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.287170] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.287177] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.287182] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.287184] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.287186] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.287189] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3491.287192] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3491.287194] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3491.287196] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3491.287197] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3491.287199] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3491.287201] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3491.287203] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3491.287204] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3491.287206] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3491.287208] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3491.287209] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3491.302829] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.302864] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.302867] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.302869] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.302872] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.302877] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.302880] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.302882] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.302885] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.302887] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.302889] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.302908] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.302911] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.302913] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.302915] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.302917] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.302920] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.302966] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.302969] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.302971] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.302973] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.302976] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.302978] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.303024] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.303027] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.303029] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.303032] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.303034] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.303036] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.303079] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.303082] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3491.303084] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3491.303086] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3491.303088] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3491.303090] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3491.303092] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3491.303141] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3491.303143] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3491.303145] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3491.303161] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3491.303163] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3491.303180] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3491.303182] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3491.303184] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3491.303207] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.303209] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.303211] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.303214] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.303216] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.303218] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.303220] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.303222] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.303224] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.303226] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.303228] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3491.303232] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3491.303233] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3491.303235] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3491.303237] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3491.303238] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3491.303240] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3491.303242] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3491.303244] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3491.303245] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3491.303247] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3491.303249] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3491.334509] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.334552] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.334556] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.334558] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.334560] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.334565] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.334568] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.334586] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.334589] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.334591] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.334594] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.334597] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.334600] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.334602] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.334604] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.334606] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.334608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.334645] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.334648] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.334650] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.334652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.334655] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.334657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.334700] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.334703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.334706] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.334708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.334710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.334712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.334756] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.334759] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3491.334761] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3491.334764] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3491.334766] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3491.334768] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3491.334770] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3491.334811] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3491.334814] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3491.334816] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3491.334818] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3491.334820] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3491.334822] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3491.334824] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3491.334825] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3491.334868] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.334870] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.334872] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.334874] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.334877] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.334879] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.334922] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.334925] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.334927] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.334929] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.334932] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3491.486657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.486699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.486701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.486723] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.486726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.486728] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.486731] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.486733] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.486735] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.486737] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.486739] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.486741] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.486743] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.486745] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.486747] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.486749] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.486751] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.486753] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.486755] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.486757] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.486759] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.486761] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.486763] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.486765] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.486767] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.486769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.486771] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.486773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.486775] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.486779] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.486782] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3491.486784] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3491.486786] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3491.486788] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3491.486790] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3491.486792] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3491.486794] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3491.837118] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.837121] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.837123] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.837126] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.837128] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.837130] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.837132] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.837134] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.837136] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.837138] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.837141] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.837143] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.837145] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.837147] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.837149] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.837151] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.837153] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.837155] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.837158] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.837160] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3491.837162] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3491.837164] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3491.837166] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3491.837168] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3491.837170] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3491.837172] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3491.837173] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3491.837175] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3491.837180] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3491.837182] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3491.837184] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3491.837186] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3491.837188] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3491.837190] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3491.837192] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3491.837194] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3491.837196] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3492.154109] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3492.154111] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3492.154113] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3492.154115] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3492.154117] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3492.154119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3492.154122] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3492.154124] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3492.154126] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3492.154128] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3492.154130] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3492.154132] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3492.154133] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3492.154135] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3492.154137] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3492.154139] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3492.154141] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3492.154148] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3492.154150] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3492.154152] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3492.154154] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3492.154156] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3492.154158] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3492.154160] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3492.154162] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3492.154164] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3492.154165] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3492.154167] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3492.154169] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3492.154171] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3492.154173] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3492.154175] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3492.154176] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3492.154178] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3492.154180] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3492.154182] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3492.154184] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3492.154186] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3492.504504] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3492.504506] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3492.504508] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3492.504510] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3492.504512] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3492.504514] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3492.504517] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3492.504519] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3492.504521] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3492.504523] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3492.504525] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3492.504527] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3492.504529] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3492.504530] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3492.504532] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3492.504534] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3492.504536] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3492.504538] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3492.504540] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3492.504541] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3492.504543] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3492.504545] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3492.504547] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3492.504549] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3492.504551] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3492.504553] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3492.504555] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3492.504557] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3492.504559] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3492.504561] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3492.504563] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3492.504564] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3492.504567] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3492.504569] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 10980
[ 3492.504572] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3492.504573] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3492.504575] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3492.504577] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3492.504579] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3492.854748] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3492.854750] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3492.854752] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3492.854754] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3492.854784] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3492.854787] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3492.854789] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3492.854791] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3492.854793] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3492.854796] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3492.854841] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3492.854843] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3492.854846] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3492.854848] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3492.854850] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3492.854852] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3492.854910] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3492.854914] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3492.854916] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3492.854918] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3492.854920] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3492.854922] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3492.854924] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3492.854955] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3492.854958] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3492.854960] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3492.854962] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3492.854964] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3492.854966] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3492.854968] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3492.854970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3492.855010] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3492.855013] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3492.855015] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3492.855017] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3492.855019] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3492.855021] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3492.855065] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3493.138685] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3493.138687] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3493.138689] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3493.138735] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3493.138738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3493.138740] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3493.138742] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3493.138744] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 5987
[ 3493.138748] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3493.138750] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3493.138791] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3493.138808] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3493.138810] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3493.138812] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3493.138814] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3493.138816] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3493.138818] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3493.138819] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3493.138821] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3493.171066] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3493.171100] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3493.171103] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3493.171105] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3493.171108] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3493.171112] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3493.171115] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3493.171117] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3493.171119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3493.171121] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3493.171123] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3493.171154] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3493.171157] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3493.171159] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3493.171161] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3493.171164] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3493.171166] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3493.171209] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3493.505266] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3493.505299] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3493.505302] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3493.505305] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3493.505307] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3493.505312] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3493.505315] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3493.505317] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3493.505319] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3493.505322] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3493.505324] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3493.505352] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3493.505356] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3493.505358] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3493.505360] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3493.505362] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3493.505365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3493.505408] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3493.505411] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3493.505413] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3493.505416] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3493.505418] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3493.505420] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3493.505464] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3493.505466] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3493.505469] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3493.505471] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3493.505473] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3493.505475] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3493.505519] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3493.505521] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3493.505523] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3493.505526] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3493.505528] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3493.505530] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3493.505532] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3493.952314] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3493.952316] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3493.952318] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3493.952320] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3493.952322] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3493.952369] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3493.952371] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3493.952373] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3493.952376] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3493.952378] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3493.952380] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3493.952425] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3493.952427] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3493.952429] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3493.952432] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3493.952434] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3493.952436] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3493.952438] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3493.952481] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3493.952484] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3493.952486] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3493.952488] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3493.952490] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3493.952492] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3493.952494] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3493.952496] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3493.952537] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3493.952540] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3493.952542] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3493.952544] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3493.952547] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3493.952549] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3493.952593] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3493.952596] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3493.952598] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3493.952601] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3493.952603] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 2793
[ 3493.952607] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3494.309157] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3494.309159] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3494.309161] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3494.309163] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3494.309165] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3494.309167] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3494.309169] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3494.309171] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3494.309173] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3494.309175] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3494.309177] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3494.309179] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3494.309180] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3494.309182] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3494.309184] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3494.309186] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3494.309188] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3494.309190] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3494.309191] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3494.309193] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3494.309195] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3494.309197] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3494.309199] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3494.309201] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3494.309203] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3494.309205] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3494.309206] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3494.309208] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3494.309210] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3494.309212] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3494.309214] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3494.309216] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3494.309218] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 2308
[ 3494.309222] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3494.309223] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3494.309225] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3494.309227] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3494.309229] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3494.309230] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3494.309232] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3494.843195] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3494.843197] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3494.843199] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3494.857562] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3494.857574] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3494.857577] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3494.857579] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3494.857581] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3494.857586] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3494.857588] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3494.857590] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3494.857592] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3494.857594] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3494.857612] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3494.857616] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3494.857619] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3494.857621] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3494.857623] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3494.857625] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3494.857627] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3494.857671] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3494.857674] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3494.857676] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3494.857678] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3494.857681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3494.857683] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3494.857726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3494.857729] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3495.190386] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3495.190405] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3495.190409] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3495.190411] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3495.190413] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3495.190416] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3495.190418] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3495.190420] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3495.190463] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3495.190466] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3495.190468] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3495.190470] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3495.190472] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3495.190475] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3495.190519] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3495.190522] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3495.190524] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3495.190527] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3495.190529] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3495.190531] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3495.190574] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3495.190577] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3495.190579] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3495.190581] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3495.190583] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3495.190585] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3495.190587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3495.190631] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3495.190633] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3495.190636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3495.190637] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3495.190640] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3495.190642] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3495.190644] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3495.190646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3495.190686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3495.190689] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3495.190691] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3495.524311] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3495.524314] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3495.524315] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3495.524358] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3495.524361] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3495.524363] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3495.524365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3495.524367] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3495.524369] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3495.524371] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3495.524373] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3495.524414] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3495.524416] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3495.524419] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3495.524421] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3495.524423] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3495.524425] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3495.524470] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3495.524472] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3495.524474] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3495.524477] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3495.524479] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 873
[ 3495.524483] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3495.524485] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3495.524525] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3495.524542] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3495.524543] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3495.524546] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3495.524548] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3495.524550] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3495.524552] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3495.524553] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3495.524555] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3495.540707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3495.540724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3495.540726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3495.540729] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3495.540731] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3495.824674] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3495.824676] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3495.824678] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3495.824681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 650
[ 3495.824684] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3495.824686] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3495.824728] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3495.824744] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3495.824746] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3495.824748] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3495.824750] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3495.824752] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3495.824754] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3495.824755] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3495.824757] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3495.841037] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3495.841052] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3495.841054] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3495.841057] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3495.841059] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3495.841064] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3495.841067] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3495.841085] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3495.841087] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3495.841089] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3495.841092] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3495.841096] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3495.841098] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3495.841101] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3495.841103] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3495.841105] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3495.841107] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3495.841145] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3495.841147] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3495.841149] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3495.841152] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3496.175223] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3496.175226] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3496.175229] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3496.175231] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3496.175233] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3496.175235] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3496.175237] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3496.175238] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3496.191335] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3496.191369] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3496.191372] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3496.191375] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3496.191377] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3496.191382] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3496.191385] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3496.191387] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3496.191390] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3496.191392] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3496.191394] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3496.191424] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3496.191426] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3496.191429] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3496.191431] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3496.191433] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3496.191435] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3496.191479] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3496.191482] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3496.191484] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3496.191486] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3496.191489] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3496.191491] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3496.191534] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3496.191537] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3496.525963] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3496.525997] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3496.526000] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3496.526002] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3496.526005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3496.526009] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3496.526012] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3496.526014] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3496.526017] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3496.526019] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3496.526021] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3496.526049] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3496.526052] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3496.526054] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3496.526056] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3496.526059] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3496.526061] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3496.526104] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3496.526107] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3496.526109] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3496.526112] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3496.526114] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3496.526116] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3496.526160] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3496.526162] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3496.526165] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3496.526167] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3496.526169] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3496.526171] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3496.526214] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3496.526217] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3496.863722] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3496.863724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3496.863727] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3496.863729] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3496.863733] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3496.863736] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3496.863738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3496.863741] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3496.863743] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3496.863760] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3496.863764] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3496.863767] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3496.863769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3496.863771] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3496.863773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3496.863776] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3496.863819] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3496.863821] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3496.863824] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3496.863826] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3496.863828] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3496.863830] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3496.863873] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3496.863876] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3496.863878] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3496.863880] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3496.863882] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3496.863884] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3496.863929] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3496.863932] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3496.863934] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3496.863937] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3496.863939] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3496.863941] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3496.863943] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3496.863986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3496.863989] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3497.192680] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3497.192682] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3497.192684] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3497.192687] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3497.192730] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3497.192733] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3497.192735] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3497.192737] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3497.192739] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3497.192741] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3497.192743] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3497.192785] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3497.192788] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3497.192790] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3497.192791] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3497.192793] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3497.192795] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3497.192797] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3497.192799] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3497.192842] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3497.192845] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3497.192847] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3497.192849] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3497.192851] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3497.192853] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3497.192899] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3497.192901] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3497.192903] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3497.192906] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3497.192908] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 167
[ 3497.192912] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3497.192914] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3497.192956] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3497.192972] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3497.192974] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3497.192976] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3497.192978] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3497.192980] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3497.192982] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3497.192984] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3497.192986] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3497.526418] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3497.526421] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3497.526423] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3497.526425] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3497.526427] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3497.526429] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3497.526431] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3497.526474] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3497.526477] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3497.526479] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3497.526481] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3497.526483] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3497.526485] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3497.526487] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3497.526488] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3497.526553] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3497.526556] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3497.526558] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3497.526560] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3497.526562] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3497.526564] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3497.526588] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3497.526591] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3497.526593] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3497.526595] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3497.526597] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 121
[ 3497.526601] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3497.526603] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3497.526643] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3497.526660] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3497.526663] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3497.526665] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3497.526667] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3497.526669] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3497.526671] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3497.526673] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3497.526675] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3497.542781] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3497.542815] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3497.843196] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 86
[ 3497.843199] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3497.843201] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3497.843203] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3497.843205] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3497.843206] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3497.843208] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3497.843210] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3497.843212] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3497.843213] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3497.843215] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3497.843217] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3497.860279] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3497.860297] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3497.860300] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3497.860302] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3497.860305] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3497.860307] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3497.860309] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3497.860311] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3497.860314] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3497.860316] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3497.860318] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3497.860320] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3497.860322] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3497.860324] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3497.860326] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3497.860328] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3497.860330] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3497.860332] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3497.860335] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3497.860337] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3497.860339] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3497.860340] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3497.860343] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3497.860344] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3498.161012] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3498.161013] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3498.161015] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3498.161017] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3498.161019] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3498.161021] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3498.161023] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3498.161024] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3498.161026] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3498.176707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3498.176815] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3498.176819] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3498.176822] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3498.176824] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3498.176830] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3498.176833] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3498.176835] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3498.176837] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3498.176840] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3498.176861] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3498.176863] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3498.176865] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3498.176867] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3498.176870] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3498.176872] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3498.176875] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3498.176877] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3498.176879] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3498.176882] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3498.176884] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3498.176887] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3498.176889] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3498.176893] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3498.176895] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3498.176897] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3498.176917] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3498.527636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3498.527979] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3498.528084] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3498.528088] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3498.528090] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3498.528093] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3498.528095] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3498.528097] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3498.528099] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3498.528101] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3498.528103] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3498.528115] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3498.528117] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3498.528119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3498.528121] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3498.528123] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3498.528125] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3498.528127] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3498.528129] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3498.528131] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3498.528133] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3498.528136] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3498.528138] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3498.528139] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3498.528142] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3498.528144] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3498.528145] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3498.528148] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3498.528149] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3498.528151] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3498.528153] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3498.528155] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3498.528157] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3498.528159] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3498.528161] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3498.528163] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3498.844152] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3498.844154] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3498.844156] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3498.844199] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3498.844202] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3498.844204] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3498.844207] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3498.844209] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3498.844211] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3498.844256] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3498.844259] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3498.844261] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3498.844264] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3498.844266] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3498.844268] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3498.844270] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3498.844312] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3498.844315] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3498.844317] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3498.844319] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3498.844321] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3498.844323] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3498.844325] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3498.844327] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3498.844367] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3498.844370] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3498.844372] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3498.844374] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3498.844376] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3498.844378] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3498.844423] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3498.844426] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3498.844429] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3498.844431] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3498.844434] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3498.844438] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3498.844440] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3498.844480] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3498.844497] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3499.177816] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3499.177817] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3499.177883] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3499.177885] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3499.177887] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3499.177889] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3499.177891] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3499.177893] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3499.177895] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3499.177897] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3499.177899] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3499.194103] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3499.194115] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3499.194117] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3499.194120] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3499.194122] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3499.194126] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3499.194128] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3499.194131] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3499.194133] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3499.194151] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3499.194153] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3499.194157] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3499.194159] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3499.194161] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3499.194163] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3499.194166] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3499.194168] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3499.194209] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3499.194212] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3499.194214] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3499.194216] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3499.194218] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3499.194221] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3499.194264] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3499.194267] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3499.194269] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3499.194271] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3499.511263] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3499.511265] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3499.511267] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3499.511269] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3499.511271] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3499.511273] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3499.511275] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3499.511277] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3499.511279] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3499.511281] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3499.511283] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3499.511285] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3499.511286] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3499.511288] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3499.511290] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3499.511292] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3499.511294] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3499.511296] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3499.511298] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3499.511299] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3499.511302] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3499.511304] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3499.511305] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3499.511307] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3499.511309] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3499.511311] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3499.511314] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3499.511316] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3499.511318] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3499.511320] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3499.511322] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3499.511324] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3499.511326] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3499.511329] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3499.511331] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3499.511333] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3499.511334] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3499.511336] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3499.511338] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3499.845232] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3499.845234] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3499.845236] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3499.845238] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3499.845240] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3499.845242] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3499.845243] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3499.845245] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3499.845247] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3499.862568] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3499.862581] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3499.862584] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3499.862586] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3499.862588] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3499.862590] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3499.862592] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3499.862594] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3499.862596] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3499.862598] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3499.862600] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3499.862602] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3499.862605] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3499.862607] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3499.862609] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3499.862611] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3499.862613] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3499.862615] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3499.862617] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3499.862619] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3499.862621] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3499.862623] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3499.862625] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3499.862627] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3499.862628] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3499.862630] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3499.862632] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3499.862634] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3500.175281] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3500.175283] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3500.175285] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3500.175330] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3500.175332] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3500.175335] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3500.175337] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3500.175339] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3500.175341] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3500.175343] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3500.175386] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3500.175388] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3500.175390] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3500.175392] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3500.175394] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3500.175396] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3500.175398] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3500.175399] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3500.175442] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3500.175444] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3500.175447] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3500.175449] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3500.175452] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3500.175454] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3500.175497] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3500.175499] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3500.175501] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3500.175504] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3500.175506] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3500.175509] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3500.175511] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3500.175553] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3500.175556] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3500.175557] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3500.175559] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3500.175561] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3500.175563] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3500.175565] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3500.175567] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3500.175569] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3500.512349] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3500.512352] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3500.512358] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3500.512360] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3500.512363] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3500.512366] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3500.512368] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3500.512371] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3500.512373] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3500.512375] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3500.512377] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3500.512379] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3500.512381] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3500.512383] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3500.512411] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3500.512414] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3500.512416] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3500.512418] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3500.512420] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3500.512422] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3500.512462] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3500.512465] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3500.512467] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3500.512470] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3500.512472] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3500.512474] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3500.512518] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3500.512520] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3500.512523] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3500.512525] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3500.512527] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3500.512529] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3500.512531] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3500.512574] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3500.512577] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3500.512579] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3500.512581] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3500.512583] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3500.862462] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3500.862479] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3500.862482] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3500.862485] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3500.862487] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3500.862492] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3500.862511] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3500.862513] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3500.862515] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3500.862518] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3500.862520] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3500.862524] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3500.862527] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3500.862529] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3500.862532] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3500.862534] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3500.862536] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3500.862569] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3500.862572] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3500.862574] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3500.862576] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3500.862579] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3500.862581] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3500.862628] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3500.862631] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3500.862633] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3500.862635] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3500.862638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3500.862640] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3500.862686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3500.862689] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3500.862691] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3500.862693] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3500.862695] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3500.862697] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3500.862699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3500.862741] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3501.179587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3501.179589] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3501.179591] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3501.179593] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3501.179595] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3501.179641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3501.179643] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3501.179645] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3501.179648] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3501.179649] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3501.179651] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3501.179654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3501.179695] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3501.179698] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3501.179699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3501.179702] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3501.179704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3501.179706] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3501.179708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3501.179710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3501.179751] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3501.179754] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3501.179756] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3501.179758] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3501.179760] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3501.179763] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3501.179807] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3501.179810] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3501.179812] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3501.179814] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3501.179816] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3501.179820] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3501.179821] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3501.179880] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3501.179882] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3501.179884] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3501.179886] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3501.179888] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3501.179890] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3501.179892] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3501.513557] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3501.513559] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3501.529739] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3501.529755] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3501.529758] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3501.529760] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3501.529762] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3501.529767] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3501.529798] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3501.529801] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3501.529804] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3501.529806] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3501.529808] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3501.529812] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3501.529815] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3501.529817] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3501.529820] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3501.529822] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3501.529824] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3501.529853] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3501.529855] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3501.529858] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3501.529860] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3501.529862] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3501.529864] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3501.529907] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3501.529909] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3501.529912] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3501.529914] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3501.529916] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3501.529918] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3501.529961] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3501.529964] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3501.529966] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3501.529968] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3501.864178] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3501.864181] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3501.864183] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3501.864185] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3501.864187] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3501.864189] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3501.864231] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3501.864234] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3501.864236] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3501.864238] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3501.864240] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3501.864242] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3501.864244] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3501.864287] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3501.864289] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3501.864291] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3501.864293] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3501.864295] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3501.864297] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3501.864299] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3501.864301] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3501.864343] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3501.864346] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3501.864348] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3501.864350] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3501.864352] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3501.864354] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3501.864400] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3501.864403] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3501.864405] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3501.864407] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3501.864410] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3501.864413] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3501.864415] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3501.864472] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3501.864474] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3501.864476] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3501.864478] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3501.864480] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3501.864481] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3502.196577] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3502.196633] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3502.196635] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3502.196637] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3502.196639] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3502.196641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3502.196677] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3502.196679] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3502.196681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3502.196684] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3502.196686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3502.196732] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3502.196735] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3502.196737] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3502.196739] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3502.196741] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3502.196744] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3502.196788] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3502.196790] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3502.196792] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3502.196795] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3502.196797] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3502.196799] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3502.196844] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3502.196846] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3502.196848] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3502.196850] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3502.196852] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3502.196855] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3502.196900] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3502.196903] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3502.196905] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3502.196907] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3502.196909] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3502.196911] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3502.531606] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3502.531608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3502.531652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3502.531655] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3502.531657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3502.531659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3502.531662] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3502.531664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3502.531708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3502.531711] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3502.531713] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3502.531715] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3502.531717] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3502.531719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3502.531722] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3502.531765] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3502.531767] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3502.531769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3502.531771] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3502.531773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3502.531775] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3502.531777] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3502.531779] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3502.531820] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3502.531823] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3502.531825] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3502.531827] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3502.531829] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3502.531831] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3502.531876] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3502.531879] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3502.531881] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3502.531883] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3502.531885] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3502.531888] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3502.531890] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3502.531944] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3502.531947] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3502.531948] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3502.881119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3502.881121] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3502.881123] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3502.881152] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3502.881155] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3502.881158] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3502.881160] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3502.881162] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3502.881164] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3502.881202] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3502.881205] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3502.881207] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3502.881210] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3502.881212] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3502.881214] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3502.881258] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3502.881261] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3502.881263] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3502.881265] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3502.881268] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3502.881270] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3502.881315] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3502.881317] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3502.881319] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3502.881322] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3502.881324] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3502.881326] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3502.881328] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3502.881371] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3502.881374] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3502.881376] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3502.881378] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3502.881379] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3502.881381] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3502.881383] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3502.881385] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3502.881428] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3502.881431] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3503.214900] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3503.214942] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3503.214945] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3503.214947] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3503.214949] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3503.214951] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3503.214953] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3503.214997] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3503.215000] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3503.215002] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3503.215004] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3503.215007] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3503.215009] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3503.215011] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3503.215056] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3503.215059] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3503.215061] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3503.215063] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3503.215065] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3503.215067] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3503.215069] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3503.215071] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3503.215112] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3503.215114] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3503.215117] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3503.215119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3503.215121] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3503.215126] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3503.215169] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3503.215172] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3503.215174] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3503.215176] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3503.215179] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3503.215182] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3503.215184] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3503.215238] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3503.215240] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3503.215242] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3503.215244] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3503.548532] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3503.548534] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3503.548537] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3503.548539] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3503.548541] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3503.548543] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3503.548545] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3503.548550] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3503.548569] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3503.548571] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3503.548573] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3503.548575] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3503.548578] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3503.548582] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3503.548584] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3503.548586] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3503.548588] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3503.548590] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3503.548593] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3503.548646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3503.548648] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3503.548650] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3503.548656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3503.548659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3503.548661] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3503.548685] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3503.548688] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3503.548690] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3503.548692] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3503.548694] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3503.548696] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3503.548698] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3503.548739] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3503.548742] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3503.548744] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3503.548746] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3503.548748] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3503.548750] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3503.882200] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3503.882219] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3503.882221] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3503.882224] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3503.882226] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3503.882228] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3503.882233] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3503.882235] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3503.882237] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3503.882240] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3503.882242] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3503.882244] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3503.882277] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3503.882280] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3503.882282] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3503.882284] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3503.882287] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3503.882289] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3503.882332] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3503.882335] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3503.882337] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3503.882340] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3503.882342] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3503.882344] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3503.882388] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3503.882391] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3503.882393] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3503.882395] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3503.882398] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3503.882400] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3503.882402] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3503.882443] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3503.882445] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3503.882447] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3503.882449] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3503.882451] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3503.882453] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3504.199416] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3504.199418] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3504.199420] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3504.199423] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3504.199425] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3504.199427] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3504.199429] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3504.199431] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3504.199435] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3504.199436] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3504.199438] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3504.199440] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3504.199442] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3504.199444] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3504.199445] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3504.199447] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3504.199449] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3504.199451] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3504.199452] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3504.215857] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3504.215874] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3504.215877] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3504.215896] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3504.215898] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3504.215904] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3504.215907] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3504.215910] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3504.215912] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3504.215914] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3504.215916] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3504.215918] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3504.215921] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3504.215923] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3504.215925] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3504.215927] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3504.215929] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3504.215957] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3504.533882] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3504.533883] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3504.533885] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3504.549527] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3504.549572] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3504.549575] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3504.549578] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3504.549580] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3504.549583] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3504.549585] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3504.549587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3504.549590] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3504.549592] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3504.549595] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3504.549615] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3504.549618] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3504.549620] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3504.549622] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3504.549624] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3504.549627] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3504.549629] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3504.549632] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3504.549634] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3504.549636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3504.549638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3504.549640] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3504.549673] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3504.549676] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3504.549678] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3504.549681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3504.549683] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3504.549685] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3504.549727] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3504.549729] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3504.549731] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3504.866656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3504.866658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3504.866660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3504.866663] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3504.866665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3504.866667] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3504.866711] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3504.866713] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3504.866715] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3504.866717] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3504.866719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3504.866721] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3504.866724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3504.866766] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3504.866769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3504.866772] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3504.866774] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3504.866776] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3504.866778] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3504.866780] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3504.866782] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3504.866822] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3504.866825] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3504.866827] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3504.866829] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3504.866832] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3504.866834] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3504.866880] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3504.866882] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3504.866885] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3504.866887] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3504.866889] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 29
[ 3504.866893] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3504.866895] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3504.866949] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3504.866951] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3504.866953] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3504.866955] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3504.866957] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3504.866959] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3505.200593] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3505.200596] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3505.200597] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3505.200600] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3505.200602] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3505.200604] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3505.200605] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3505.200607] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3505.200609] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3505.216833] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3505.216840] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3505.216842] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3505.216844] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3505.216847] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3505.216849] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3505.216877] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3505.216880] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3505.216882] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3505.216884] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3505.216886] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3505.216891] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3505.216894] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3505.216896] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3505.216898] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3505.216900] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3505.216902] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3505.216930] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3505.216932] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3505.216935] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3505.216937] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3505.216940] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3505.216942] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3505.216982] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3505.216985] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3505.216987] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3505.216990] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3505.216992] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3505.550591] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3505.550609] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3505.550611] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3505.550614] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3505.550616] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3505.550622] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3505.550624] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3505.550627] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3505.550629] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3505.550632] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3505.550634] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3505.550692] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3505.550696] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3505.550698] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3505.550701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3505.550703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3505.550726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3505.550729] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3505.550732] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3505.550734] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3505.550736] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3505.550738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3505.550740] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3505.550742] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3505.551271] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3505.551276] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3505.551278] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3505.551281] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3505.551283] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3505.551285] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3505.551287] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3505.551289] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3505.551291] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3505.551293] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3505.551295] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3505.551297] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3505.551299] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3505.551301] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3506.231110] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3506.231112] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3506.231115] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3506.231117] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3506.231119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3506.231151] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3506.231154] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3506.231156] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3506.231159] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3506.231161] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3506.231163] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3506.231207] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3506.231210] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3506.231212] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3506.231215] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3506.231217] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3506.231219] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3506.231263] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3506.231265] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3506.231267] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3506.231270] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3506.231272] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3506.231274] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3506.231318] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3506.231321] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3506.231323] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3506.231325] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3506.231327] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3506.231329] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3506.231331] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3506.231374] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3506.231377] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3506.231379] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3506.231381] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3506.231383] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3506.231385] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3506.231386] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3506.231388] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3506.551962] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3506.551965] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3506.551967] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3506.551969] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3506.551972] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3506.552006] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3506.552009] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3506.552011] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3506.552013] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3506.552015] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3506.552017] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3506.552019] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3506.552083] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3506.552086] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3506.552088] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3506.552090] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3506.552092] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3506.552094] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3506.552096] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3506.552098] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3506.552121] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3506.552124] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3506.552126] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3506.552128] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3506.552130] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3506.552132] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3506.552173] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3506.552176] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3506.552178] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3506.552180] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3506.552183] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 4758
[ 3506.552187] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3506.552189] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3506.552229] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3506.552246] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3506.552249] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3506.552251] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3506.885585] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3506.885587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3506.885631] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3506.885633] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3506.885636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3506.885638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3506.885640] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3506.885642] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3506.885644] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3506.885687] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3506.885689] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3506.885691] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3506.885693] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3506.885695] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3506.885697] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3506.885699] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3506.885701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3506.885743] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3506.885745] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3506.885747] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3506.885749] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3506.885751] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3506.885754] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3506.885799] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3506.885802] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3506.885804] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3506.885806] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3506.885808] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 12616
[ 3506.885812] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3506.885814] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3506.885856] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3506.885872] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3506.885874] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3506.885876] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3506.885878] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3506.885880] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3506.885881] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3506.885883] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3506.885885] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3507.186078] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 17756
[ 3507.186081] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3507.186083] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3507.186124] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3507.186140] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3507.186142] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3507.186144] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3507.186146] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3507.186148] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3507.186150] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3507.186151] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3507.186153] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3507.202332] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3507.202365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3507.202368] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3507.202371] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3507.202373] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3507.202379] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3507.202381] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3507.202384] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3507.202386] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3507.202388] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3507.202390] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3507.202421] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3507.202424] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3507.202426] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3507.202428] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3507.202431] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3507.202433] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3507.202476] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3507.202479] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3507.202481] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3507.202483] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3507.202485] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3507.202488] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3507.202531] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3507.536794] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3507.536796] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3507.536837] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3507.536853] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3507.536855] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3507.536857] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3507.536859] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3507.536861] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3507.536862] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3507.536864] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3507.536866] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3507.552736] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3507.552750] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3507.552752] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3507.552755] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3507.552757] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3507.552759] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3507.552762] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3507.552780] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3507.552782] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3507.552785] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3507.552787] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3507.552791] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3507.552794] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3507.552796] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3507.552798] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3507.552800] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3507.552802] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3507.552840] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3507.552843] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3507.552845] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3507.552848] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3507.552850] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3507.552852] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3507.552896] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3507.552898] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3507.552900] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3507.869883] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3507.869885] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3507.869887] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3507.869889] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3507.869891] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3507.869938] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3507.869940] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3507.869942] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3507.869944] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3507.869946] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3507.869948] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3507.869950] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3507.869993] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3507.869995] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3507.869998] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3507.869999] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3507.870001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3507.870003] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3507.870005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3507.870007] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3507.870050] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3507.870053] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3507.870055] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3507.870058] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3507.870060] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3507.870062] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3507.870106] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3507.870109] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3507.870111] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3507.870113] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3507.870116] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 25126
[ 3507.870119] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3507.870121] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3507.870165] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3507.870181] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3507.870184] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3507.870185] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3507.870187] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3507.870189] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3507.870191] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3508.170225] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3508.170228] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3508.170230] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3508.170232] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3508.170234] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3508.170275] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3508.170277] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3508.170279] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3508.170281] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3508.170283] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3508.170285] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3508.170287] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3508.170289] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3508.170332] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3508.170334] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3508.170337] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3508.170339] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3508.170341] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3508.170343] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3508.170397] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3508.170399] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3508.170401] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3508.170404] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3508.170406] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 27072
[ 3508.170410] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3508.170411] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3508.170444] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3508.170462] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3508.170465] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3508.170467] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3508.170469] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3508.170470] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3508.170472] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3508.170474] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3508.170476] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3508.186718] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3508.186734] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3508.186737] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3508.186756] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3508.353873] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3508.353875] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3508.353876] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3508.353878] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3508.353920] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3508.353922] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3508.353924] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3508.353927] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3508.353929] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3508.353931] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3508.353975] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3508.353978] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3508.353980] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3508.353982] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3508.353984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 28060
[ 3508.353988] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3508.353989] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3508.354031] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3508.354033] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3508.354050] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3508.354052] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3508.354054] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3508.354056] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3508.354058] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3508.354060] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3508.354061] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3508.370179] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3508.370214] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3508.370216] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3508.370219] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3508.370221] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3508.370226] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3508.370229] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3508.370231] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3508.370233] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3508.370236] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3508.370238] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3508.660948] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3508.660950] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3508.660992] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3508.660994] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3508.660997] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3508.660999] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3508.661001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3508.661003] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3508.661048] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3508.661050] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3508.661052] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3508.661055] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3508.661057] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 28875
[ 3508.661060] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3508.661062] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3508.661103] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3508.661105] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3508.661121] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3508.661123] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3508.661125] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3508.661127] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3508.661128] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3508.661130] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3508.661132] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3508.693280] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3508.693293] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3508.693295] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3508.693298] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3508.693318] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3508.693322] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3508.693325] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3508.986766] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3508.986768] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3508.986770] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3508.986772] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3508.986774] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3508.986776] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3508.986777] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3509.018788] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3509.018801] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3509.018803] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3509.018805] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3509.018807] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3509.018830] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3509.018833] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3509.018835] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3509.018837] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3509.018840] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3509.018842] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3509.018885] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3509.018888] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3509.018890] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3509.018892] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3509.018895] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3509.018897] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3509.018939] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3509.018942] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3509.018945] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3509.018947] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3509.018950] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3509.018952] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3509.507180] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3509.507224] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3509.507228] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3509.507230] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3509.507232] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3509.507238] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3509.507259] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3509.507262] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3509.507264] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3509.507266] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3509.507269] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3509.507273] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3509.507275] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3509.507278] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3509.507280] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3509.507282] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3509.507284] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3509.507318] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3509.507321] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3509.507323] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3509.507325] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3509.507328] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3509.507330] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3509.507375] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3509.507378] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3509.507380] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3509.507383] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3509.507385] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3509.507387] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3509.507429] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3509.507432] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3509.507434] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3509.507436] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3509.507438] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3509.507440] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3509.825417] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3509.825419] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3509.825452] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3509.825455] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3509.825457] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3509.825459] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3509.825462] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3509.825464] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3509.825509] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3509.825512] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3509.825514] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3509.825516] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3509.825518] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3509.825520] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3509.825568] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3509.825570] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3509.825573] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3509.825575] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3509.825577] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3509.825579] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3509.825621] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3509.825624] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3509.825626] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3509.825628] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3509.825630] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3509.825632] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3509.825634] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3509.825677] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3509.825679] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3509.825681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3509.825684] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3509.825686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3509.825688] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3509.825690] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3509.825692] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3509.825733] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3509.825735] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3509.825738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3510.205830] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3510.205846] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3510.205848] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3510.205850] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3510.205852] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3510.205854] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3510.205856] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3510.205858] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3510.222021] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3510.222033] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3510.222036] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3510.222039] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3510.222041] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3510.222043] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3510.222045] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3510.222047] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3510.222066] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3510.222068] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3510.222071] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3510.222074] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3510.222077] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3510.222079] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3510.222081] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3510.222083] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3510.222085] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3510.222122] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3510.222125] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3510.222127] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3510.222129] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3510.222131] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3510.222134] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3510.222177] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3510.222179] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3510.222181] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3510.222183] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3510.222186] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3510.572454] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3510.572472] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3510.572476] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3510.572478] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3510.572480] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3510.572483] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3510.572486] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3510.572488] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3510.572531] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3510.572534] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3510.572536] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3510.572538] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3510.572541] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3510.572543] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3510.572587] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3510.572590] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3510.572592] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3510.572594] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3510.572596] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3510.572598] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3510.572642] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3510.572645] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3510.572647] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3510.572649] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3510.572651] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3510.572653] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3510.572656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3510.572698] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3510.572701] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3510.572703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3510.572704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3510.572706] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3510.572708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3510.572710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3510.572712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3510.572755] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3510.572757] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3510.572759] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3510.907321] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3510.907323] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3510.907325] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3510.907328] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3510.907330] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3510.907369] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3510.907372] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3510.907374] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3510.907376] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3510.907378] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3510.907381] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3510.907424] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3510.907427] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3510.907429] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3510.907432] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3510.907434] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3510.907436] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3510.907480] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3510.907483] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3510.907485] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3510.907487] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3510.907489] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3510.907491] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3510.907493] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3510.907547] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3510.907550] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3510.907551] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3510.907553] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3510.907556] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3510.907558] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3510.907560] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3510.907562] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3510.907592] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3510.907595] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3510.907597] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3510.907599] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3510.907602] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3510.907604] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3511.236708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3511.236710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3511.236712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3511.236754] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3511.236756] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3511.236758] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3511.236760] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3511.236763] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3511.236765] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3511.236810] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3511.236813] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3511.236815] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3511.236817] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3511.236819] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32255
[ 3511.236823] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3511.236824] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3511.236866] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3511.236868] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3511.236885] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3511.236887] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3511.236889] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3511.236891] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3511.236893] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3511.236895] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3511.236904] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3511.243698] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3511.243726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3511.243729] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3511.243732] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3511.243734] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3511.243739] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3511.243741] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3511.243744] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3511.243746] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3511.243748] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3511.243750] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3511.243779] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3511.573499] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3511.573501] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3511.573503] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3511.573506] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3511.573551] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3511.573553] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3511.573555] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3511.573558] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3511.573560] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3511.573562] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3511.573605] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3511.573608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3511.573624] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3511.573626] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3511.573628] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3511.573644] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3511.573646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3511.573664] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3511.573667] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3511.573682] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3511.573684] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3511.573686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3511.573703] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3511.573705] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3511.573707] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3511.573709] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3511.573712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3511.573715] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3511.573717] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3511.573719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3511.573721] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3511.573723] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3511.573726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3511.573728] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3511.573730] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3511.573732] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32402
[ 3511.573735] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3511.573737] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3511.907349] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3511.907352] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3511.907354] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3511.907356] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3511.907359] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3511.907361] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3511.907405] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3511.907407] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3511.907410] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3511.907412] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3511.907414] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3511.907416] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3511.907472] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3511.907475] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3511.907477] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3511.907480] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3511.907482] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3511.907484] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3511.907486] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3511.907517] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3511.907520] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3511.907522] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3511.907524] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3511.907526] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3511.907528] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3511.907531] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3511.907533] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3511.907573] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3511.907576] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3511.907578] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3511.907580] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3511.907582] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3511.907584] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3511.907629] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3511.907631] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3511.907633] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3511.907636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3511.907638] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32502
[ 3512.224375] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3512.224377] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3512.224379] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3512.224381] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3512.224427] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3512.224429] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3512.224431] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3512.224434] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3512.224436] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32569
[ 3512.224439] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3512.224441] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3512.224483] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3512.224499] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3512.224501] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3512.224502] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3512.224504] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3512.224506] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3512.224508] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3512.224510] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3512.224511] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3512.240685] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3512.240718] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3512.240721] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3512.240724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3512.240726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3512.240731] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3512.240733] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3512.240736] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3512.240738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3512.240740] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3512.240743] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3512.240769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3512.240771] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3512.240773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3512.240776] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3512.240778] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3512.240780] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3512.541764] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3512.541766] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3512.541770] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3512.541774] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3512.541776] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3512.541778] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3512.541780] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3512.541782] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3512.541815] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3512.541818] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3512.541820] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3512.541822] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3512.541824] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3512.541827] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3512.541869] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3512.541872] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3512.541875] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3512.541877] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3512.541879] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3512.541881] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3512.541925] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3512.541927] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3512.541929] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3512.541932] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3512.541934] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3512.541936] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3512.541980] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3512.541983] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3512.541985] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3512.541987] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3512.541989] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3512.541992] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3512.541994] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3512.542039] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3512.542042] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3512.542044] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3512.542046] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3512.908898] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3512.908915] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3512.908917] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3512.908937] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3512.908940] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3512.908942] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3512.908945] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3512.908947] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3512.908950] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3512.908952] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3512.908954] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3512.908958] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3512.908961] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3512.908963] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3512.908966] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3512.908968] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3512.908970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3512.908995] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3512.908998] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3512.909000] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3512.909003] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3512.909005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3512.909007] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3512.909050] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3512.909053] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3512.909055] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3512.909057] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3512.909060] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3512.909062] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3512.909105] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3512.909108] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3512.909110] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3512.909112] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3512.909114] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3512.909116] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3512.909119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3513.225324] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3513.225326] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3513.225368] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3513.225371] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3513.225373] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3513.225375] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3513.225377] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3513.225379] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3513.225425] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3513.225428] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3513.225430] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3513.225432] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3513.225434] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32684
[ 3513.225438] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3513.225440] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3513.225480] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3513.225482] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3513.225498] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3513.225500] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3513.225502] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3513.225504] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3513.225506] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3513.225508] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3513.225510] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3513.241724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3513.241758] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3513.241761] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3513.241764] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3513.241766] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3513.241771] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3513.241773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3513.241776] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3513.241778] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3513.241781] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3513.241783] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3513.241805] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3513.241808] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3513.575580] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3513.575636] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3513.575639] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3513.575641] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3513.575643] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3513.575645] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3513.575647] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3513.575649] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3513.575681] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3513.575684] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3513.575686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3513.575688] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3513.575690] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3513.575691] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3513.575693] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3513.575695] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3513.575738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3513.575740] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3513.575743] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3513.575745] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3513.575747] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3513.575749] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3513.575792] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3513.575795] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3513.575797] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3513.575799] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3513.575801] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32704
[ 3513.575805] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3513.575806] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3513.575848] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3513.575864] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3513.575867] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3513.575869] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3513.575871] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3513.575872] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3513.575874] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3513.575876] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3513.575878] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3513.592104] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3513.909140] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3513.909142] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3513.909144] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3513.909147] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3513.909149] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3513.909181] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3513.909184] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3513.909186] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3513.909189] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3513.909191] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3513.909193] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3513.909235] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3513.909237] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3513.909240] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3513.909243] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3513.909245] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3513.909247] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3513.909292] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3513.909294] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3513.909296] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3513.909299] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3513.909301] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3513.909303] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3513.909305] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3513.909347] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3513.909349] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3513.909351] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3513.909353] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3513.909355] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3513.909357] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3513.909359] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3513.909361] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3513.909402] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3513.909404] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3513.909406] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3513.909409] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3513.909411] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3513.909413] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3514.242560] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3514.242562] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3514.242564] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3514.242566] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3514.242568] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3514.242571] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3514.242613] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3514.242616] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3514.242618] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3514.242620] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3514.242623] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3514.242625] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3514.242656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3514.242658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3514.242661] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3514.242663] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3514.242665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3514.242667] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3514.242711] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3514.242714] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3514.242716] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3514.242718] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3514.242720] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3514.242722] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3514.242724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3514.242767] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3514.242769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3514.242771] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3514.242773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3514.242775] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3514.242777] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3514.242780] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3514.242782] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3514.242822] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3514.242824] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3514.242827] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3514.242829] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3514.242831] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3514.550811] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3514.550813] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3514.550815] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3514.550817] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3514.550843] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3514.550845] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3514.550876] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3514.550878] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3514.550880] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3514.550882] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3514.550884] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3514.550886] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3514.550887] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3514.550889] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3514.550891] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3514.577119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3514.577153] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3514.577156] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3514.577158] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3514.577160] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3514.577165] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3514.577168] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3514.577170] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3514.577172] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3514.577175] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3514.577178] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3514.577204] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3514.577207] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3514.577209] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3514.577211] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3514.577214] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3514.906566] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3514.906568] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3514.906612] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3514.906615] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3514.906617] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3514.906619] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3514.906622] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3514.906624] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3514.906669] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3514.906672] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3514.906674] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3514.906676] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3514.906678] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3514.906680] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3514.906682] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3514.906724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3514.906726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3514.906729] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3514.906731] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3514.906733] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3514.906735] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3514.906737] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3514.906739] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3514.906781] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3514.906783] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3514.906786] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3514.906788] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3514.906790] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3514.906793] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3514.906842] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3514.906845] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3514.906848] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3514.906850] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3514.906852] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3514.906856] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3514.906857] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3514.906908] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3514.906911] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3514.906913] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3515.226944] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3515.226946] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3515.226948] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3515.226950] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3515.226952] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3515.226954] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3515.226958] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3515.226960] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3515.226962] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3515.226964] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3515.226966] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3515.226968] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3515.226970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3515.226972] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3515.226974] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3515.226976] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3515.226978] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3515.226980] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3515.226982] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3515.226983] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3515.226985] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3515.226987] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3515.226989] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3515.226991] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3515.226993] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3515.226995] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3515.226997] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3515.226999] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3515.227001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3515.227003] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3515.227005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3515.227007] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3515.227010] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3515.227012] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3515.227013] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3515.227015] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3515.227017] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3515.227019] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3515.227020] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3515.227022] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3515.560935] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3515.560937] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3515.560939] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3515.560941] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3515.560944] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3515.560948] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3515.560951] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3515.560953] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3515.560955] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3515.560957] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3515.560959] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3515.560986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3515.560988] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3515.560991] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3515.560993] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3515.560995] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3515.560997] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3515.561039] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3515.561042] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3515.561044] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3515.561046] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3515.561048] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3515.561050] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3515.561103] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3515.561107] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3515.561109] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3515.561111] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3515.561113] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3515.561115] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3515.561118] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3515.561153] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3515.561156] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3515.561158] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3515.561160] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3515.561162] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3515.561164] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3515.561166] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3515.561168] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3515.894332] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3515.894334] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3515.894336] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3515.894338] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3515.894340] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3515.894342] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3515.894344] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3515.894346] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3515.894348] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3515.894350] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3515.894352] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3515.894354] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3515.894356] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3515.894358] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3515.894360] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3515.894362] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3515.894363] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3515.894365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3515.894367] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3515.894369] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3515.894371] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3515.894372] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3515.894374] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3515.894376] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3515.894378] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3515.894380] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3515.894382] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3515.894384] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3515.894386] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3515.894388] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3515.894390] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3515.894392] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3515.894396] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3515.894399] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3515.894401] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3515.894403] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3515.894406] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3515.894408] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3515.894410] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3516.208944] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3516.208946] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3516.208949] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3516.208951] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3516.208953] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3516.208996] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3516.208999] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3516.209001] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3516.209003] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3516.209005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3516.209007] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3516.209062] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3516.209065] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3516.209067] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3516.209069] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3516.209071] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3516.209073] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3516.209113] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3516.209115] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3516.209117] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3516.209119] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3516.209122] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3516.209123] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3516.209125] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3516.209167] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3516.209170] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3516.209172] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3516.209174] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3516.209176] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3516.209178] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3516.209180] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3516.209182] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3516.209225] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3516.209228] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3516.209230] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3516.209232] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3516.209234] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3516.209236] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3516.544960] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3516.544962] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3516.544964] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3516.544966] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3516.544968] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3516.544970] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3516.544972] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3516.544977] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3516.544979] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3516.544981] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3516.544983] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3516.544985] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3516.544987] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3516.544989] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3516.544991] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3516.544992] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3516.544994] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3516.544996] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3516.544998] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3516.545000] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3516.545002] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3516.545004] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3516.545005] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3516.545007] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3516.545009] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3516.545011] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3516.545014] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3516.545016] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3516.545018] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3516.545020] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3516.545022] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3516.545024] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3516.545026] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3516.545029] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3516.545031] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3516.545032] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3516.545034] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3516.545036] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3516.545037] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3516.545039] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3516.895313] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3516.895315] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3516.895317] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3516.895319] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3516.895322] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3516.895354] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3516.895357] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3516.895359] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3516.895361] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3516.895363] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3516.895365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3516.895367] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3516.895410] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3516.895413] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3516.895414] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3516.895417] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3516.895419] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3516.895421] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3516.895423] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3516.895425] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3516.895465] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3516.895468] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3516.895470] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3516.895472] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3516.895474] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3516.895476] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3516.895523] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3516.895525] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3516.895528] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3516.895530] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3516.895532] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3516.895535] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3516.895537] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3516.895591] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3516.895593] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3516.895595] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3516.895597] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3516.895599] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3516.895601] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3516.895602] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3517.228832] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3517.228860] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3517.228863] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3517.228865] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3517.228867] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3517.228870] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3517.228872] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3517.228916] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3517.228918] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3517.228920] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3517.228923] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3517.228925] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3517.228927] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3517.228971] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3517.228973] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3517.228975] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3517.228977] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3517.228979] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3517.228982] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3517.229026] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3517.229029] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3517.229031] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3517.229033] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3517.229036] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3517.229038] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3517.229040] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3517.229085] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3517.229088] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3517.229090] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3517.229092] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3517.229094] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3517.229096] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3517.229098] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3517.229100] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3517.229140] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3517.229143] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3517.229145] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3517.229147] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3517.562489] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3517.562492] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3517.562495] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3517.562498] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3517.562500] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3517.562502] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3517.562504] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3517.562507] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3517.562549] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3517.562552] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3517.562554] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3517.562556] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3517.562558] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3517.562560] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3517.562600] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3517.562603] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3517.562605] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3517.562607] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3517.562609] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3517.562612] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3517.562654] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3517.562657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3517.562659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3517.562661] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3517.562663] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3517.562665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3517.562668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3517.562709] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3517.562712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3517.562714] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3517.562716] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3517.562718] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3517.562720] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3517.562722] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3517.562724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3517.562766] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3517.562769] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3517.562771] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3518.229693] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3518.229695] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3518.229749] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3518.229752] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3518.229754] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3518.229756] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3518.229758] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3518.229760] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3518.229762] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3518.229799] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3518.229802] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3518.229804] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3518.229806] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3518.229808] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3518.229810] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3518.229811] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3518.229813] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3518.229855] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3518.229857] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3518.229860] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3518.229862] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3518.229864] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3518.229866] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3518.229910] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3518.229913] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3518.229915] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3518.229917] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3518.229920] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3518.229922] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3518.229967] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3518.229969] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3518.229971] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3518.229973] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3518.229989] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3518.229991] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3518.229993] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3518.229995] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3518.229997] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3518.229999] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3518.230002] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3518.596325] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3518.596327] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3518.596345] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3518.596348] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3518.596350] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3518.596352] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3518.596354] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3518.596365] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3518.596368] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3518.596370] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3518.596372] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3518.596375] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3518.596377] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3518.596379] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3518.596382] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3518.596399] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3518.596401] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3518.596403] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3518.596405] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3518.596414] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3518.596417] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3518.596419] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3518.596421] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3518.596423] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3518.596425] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3518.596464] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3518.596467] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3518.596469] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3518.596471] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3518.596474] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3518.596476] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3518.596478] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3518.596520] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3518.596522] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3518.596524] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3518.596526] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3518.596528] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3518.596530] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3518.914096] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3518.914098] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3518.914100] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3518.914143] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3518.914146] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3518.914148] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3518.914150] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3518.914152] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3518.914154] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3518.914156] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3518.914157] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3518.914199] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3518.914201] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3518.914204] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3518.914206] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3518.914208] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3518.914210] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3518.914255] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3518.914258] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3518.914260] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3518.914262] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3518.914265] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3518.914268] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3518.914270] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3518.914324] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3518.914326] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3518.914328] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3518.914330] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3518.914332] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3518.914334] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3518.914336] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3518.914338] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3518.914339] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3518.930515] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3518.930532] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3518.930534] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3518.930537] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3518.930539] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3518.930544] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3519.247880] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3519.247882] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3519.247884] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3519.247886] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3519.247889] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3519.247906] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3519.247909] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3519.247912] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3519.247914] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3519.247917] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3519.247919] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3519.247921] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3519.247924] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3519.247926] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3519.247977] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3519.247979] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3519.247981] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3519.247983] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3519.247985] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3519.247987] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3519.247989] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3519.247991] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3519.247993] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3519.264165] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3519.264200] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3519.264203] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3519.264205] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3519.264208] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3519.264212] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3519.264215] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3519.264218] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3519.264220] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3519.264222] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3519.264225] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3519.264253] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3519.264256] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3519.264258] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3519.564918] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3519.564922] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3519.564923] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3519.564977] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3519.564980] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3519.564981] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3519.564983] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3519.564985] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3519.564987] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3519.564989] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3519.564991] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3519.564993] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3519.581338] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3519.581341] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3519.581343] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3519.581346] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3519.581348] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3519.581350] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3519.581354] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3519.581356] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3519.581358] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3519.581360] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3519.581363] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3519.581364] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3519.581366] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3519.581368] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3519.581370] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3519.581372] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3519.581374] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3519.581376] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3519.581378] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3519.581380] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3519.581382] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3519.581384] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3519.581386] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3519.581388] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3519.581390] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3519.581393] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3519.581395] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3519.914886] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3519.914888] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3519.914921] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3519.914924] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3519.914926] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3519.914929] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3519.914931] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3519.914933] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3519.914977] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3519.914979] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3519.914981] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3519.914984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3519.914986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3519.914988] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3519.915032] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3519.915035] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3519.915037] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3519.915039] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3519.915041] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3519.915043] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3519.915045] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3519.915088] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3519.915090] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3519.915092] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3519.915094] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3519.915096] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3519.915098] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3519.915100] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3519.915102] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3519.915145] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3519.915147] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3519.915149] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3519.915152] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3519.915154] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3519.915156] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3519.915200] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3519.915203] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3519.915205] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3520.232221] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3520.232223] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3520.232279] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3520.232281] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3520.232283] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3520.232285] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3520.232287] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3520.232289] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3520.232291] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3520.232292] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3520.232294] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3520.248608] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3520.248621] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3520.248624] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3520.248627] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3520.248646] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3520.248648] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3520.248650] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3520.248652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3520.248655] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3520.248657] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3520.248659] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3520.248663] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3520.248666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3520.248668] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3520.248670] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3520.248672] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3520.248675] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3520.248704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3520.248706] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3520.248708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3520.248711] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3520.248713] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3520.248715] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3520.248760] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3520.248763] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3520.248765] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3520.582364] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3520.582366] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3520.582376] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3520.582379] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3520.582381] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3520.582383] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3520.582385] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3520.582387] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3520.582389] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3520.582391] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3520.582393] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3520.582395] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3520.582397] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3520.582399] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3520.582401] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3520.582403] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3520.582405] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3520.582407] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3520.582409] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3520.582411] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3520.582413] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3520.582415] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3520.582416] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3520.582418] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3520.582420] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3520.582422] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3520.582424] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3520.582425] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3520.582427] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3520.582429] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3520.582431] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3520.582433] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3520.582435] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3520.582449] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3520.582452] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3520.582453] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3520.582456] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3520.582458] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3520.882658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3520.882660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3520.882692] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3520.882696] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3520.882698] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3520.882700] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3520.882702] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3520.882704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3520.882706] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3520.882749] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3520.882751] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3520.882753] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3520.882755] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3520.882758] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3520.882759] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3520.882761] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3520.882763] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3520.882805] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3520.882807] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3520.882810] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3520.882812] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3520.882814] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3520.882816] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3520.882862] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3520.882864] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3520.882866] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3520.882869] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3520.882871] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3520.882875] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3520.882877] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3520.882929] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3520.882932] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3520.882934] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3520.882936] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3520.882938] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3520.882940] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3520.882942] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3520.882944] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3520.882946] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3521.233149] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3521.233153] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3521.233156] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3521.233158] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3521.233160] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3521.233162] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3521.233164] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3521.233166] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3521.233188] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3521.233191] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3521.233193] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3521.233195] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3521.233197] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3521.233199] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3521.233201] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3521.233205] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3521.233208] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3521.233210] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3521.233212] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3521.233214] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3521.233216] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3521.233245] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3521.233247] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3521.233249] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3521.233251] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3521.233253] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3521.233257] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3521.233259] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3521.233313] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3521.233316] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3521.233318] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3521.233320] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3521.233322] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3521.233324] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3521.233326] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3521.233327] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3521.233329] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3521.250082] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3521.250095] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3521.567764] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3521.567766] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3521.567811] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3521.567815] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3521.567816] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3521.567819] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3521.567821] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3521.567823] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3521.567825] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3521.567867] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3521.567869] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3521.567871] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3521.567873] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3521.567875] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3521.567877] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3521.567879] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3521.567881] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3521.567922] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3521.567925] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3521.567927] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3521.567929] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3521.567932] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3521.567934] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3521.567979] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3521.567981] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3521.567984] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3521.567986] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3521.567988] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3521.567992] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3521.567994] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3521.568065] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3521.568068] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3521.568070] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3521.568071] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3521.568073] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3521.568075] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3521.568077] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3521.568079] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3521.568081] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3521.900550] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3521.900552] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3521.900598] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3521.900600] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3521.900602] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3521.900605] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3521.900607] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3521.900609] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3521.900610] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3521.900652] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3521.900655] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3521.900656] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3521.900658] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3521.900660] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3521.900663] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3521.900665] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3521.900666] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3521.900711] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3521.900713] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3521.900715] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3521.900718] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3521.900719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3521.900721] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3521.900766] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3521.900768] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3521.900770] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3521.900773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3521.900775] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3521.900779] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3521.900781] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3521.900836] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3521.900838] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3521.900840] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3521.900842] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3521.900843] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3521.900846] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3521.900847] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3521.900849] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3521.900851] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3522.234345] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3522.234347] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3522.234349] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3522.263738] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3522.263752] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3522.263773] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3522.263776] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3522.263779] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3522.263783] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3522.263786] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3522.263788] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3522.263791] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3522.263793] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3522.263795] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3522.263829] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3522.263833] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3522.263835] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3522.263837] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3522.263839] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3522.263842] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3522.263885] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3522.263888] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3522.263890] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3522.263893] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3522.263895] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3522.263897] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3522.263941] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3522.263943] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3522.263945] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3522.263947] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3522.263949] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3522.263952] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3522.263997] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3522.264000] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3522.264002] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3522.584395] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3522.584396] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3522.584404] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3522.584406] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3522.584408] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3522.584410] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3522.584412] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3522.584414] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3522.584416] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3522.584418] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3522.584420] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3522.584421] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3522.584423] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3522.584425] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3522.584427] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3522.584429] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3522.584431] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3522.584432] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3522.584434] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3522.584436] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3522.584438] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3522.584440] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3522.584441] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3522.584443] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3522.584445] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3522.584447] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3522.584449] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3522.584451] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3522.584453] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3522.584455] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3522.584459] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3522.584462] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3522.584464] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3522.584466] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG] before dump_readable_md UpMappingStrength= 32728
[ 3522.584470] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3522.584471] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3522.584473] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3522.934654] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3522.934656] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3522.934658] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3522.934659] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3522.934661] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3522.934663] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3522.934669] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3522.934671] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3522.934673] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3522.934674] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3522.934676] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3522.934684] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3522.934686] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3522.934688] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3522.934690] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3522.934692] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  207] confidence_noPR( 255)
[ 3522.934694] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  208] dBrightness_noPR( 128)
[ 3522.934696] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  209] dSaturationPlusOne_noPR( 160)
[ 3522.934698] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  210] dContrastPlusOne_noPR( 160)
[ 3522.934700] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1876] gainPos_precisionRendering(  29)
[ 3522.934702] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1877] gainPos_dLocalContrast(  29)
[ 3522.934704] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1878] gainPos_dBrightness(  29)
[ 3522.934706] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1879] gainPos_dSaturation(  29)
[ 3522.934708] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1880] gainPos_dContrast(  29)
[ 3522.934710] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1881] gainNeg_dSaturation(  29)
[ 3522.934712] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1882] gainNeg_dContrast(  29)
[ 3522.934714] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1884] dBrightness(9830)
[ 3522.934715] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1885] dContrast(3277)
[ 3522.934717] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1886] dColorShift(   0)
[ 3522.934719] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1887] dSaturation(3277)
[ 3522.934721] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1888] dBacklight(32768)
[ 3522.934723] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1889] dLocalContrast(4915)
[ 3522.934724] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][pq_process, 1890] dBrightness_PR_on(11469)
[ 3522.934726] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  201] confidence( 255)
[ 3522.934728] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  202] precisionRenderingStrength( 128)
[ 3522.934730] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  203] dLocalContrast( 128)
[ 3522.934732] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  204] dBrightness( 128)
[ 3522.934734] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  205] dSaturationPlusOne( 160)
[ 3522.934736] mtk_tty_vuart: [SYS][ERROR] [DolbyDBG][get_dual_ended_info_from_md,  206] dContrastPlusOne( 160)
[ 3523.251610] mtk_tty_vuart: [DolbyDBG]Level 17
[ 3523.251612] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]mid_boost:              128
[ 3523.251614] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]highlight_stretch:      60
[ 3523.251616] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]shadow_drop:            80
[ 3523.251618] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]contrast_boost:         128
[ 3523.251636] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]saturation_boost:       128
[ 3523.251638] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]detail_boost:           128
[ 3523.251641] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_indicator:       0
[ 3523.251643] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]intensity_indicator_PQ: 2081
[ 3523.251645] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]revision:               0
[ 3523.251647] mtk_tty_vuart: [SYS][ERROR]      [DolbyDBG]chroma_lift:            128
[ 3523.251656] mtk_tty^C
130|console:/ #
